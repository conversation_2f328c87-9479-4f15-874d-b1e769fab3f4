using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace VideoCutterPro.Services
{
    /// <summary>
    /// Advanced Time Management Service (Bandicut Style)
    /// نظام إدارة الوقت المتقدم (نمط Bandicut)
    /// </summary>
    public static class AdvancedTimeService
    {
        #region Time Formatting (تنسيق الوقت)
        
        /// <summary>
        /// Format time in Bandicut style: H:MM:SS.FF
        /// تنسيق الوقت بنمط Bandicut: H:MM:SS.FF
        /// </summary>
        public static string FormatTimeBandicut(TimeSpan time)
        {
            if (time < TimeSpan.Zero)
                time = TimeSpan.Zero;
                
            return $"{(int)time.TotalHours}:{time.Minutes:D2}:{time.Seconds:D2}.{time.Milliseconds / 10:D2}";
        }
        
        /// <summary>
        /// Format time for display: HH:MM:SS or MM:SS
        /// تنسيق الوقت للعرض: HH:MM:SS أو MM:SS
        /// </summary>
        public static string FormatTimeDisplay(TimeSpan time)
        {
            if (time < TimeSpan.Zero)
                time = TimeSpan.Zero;
                
            if (time.TotalHours >= 1)
                return $"{(int)time.TotalHours}:{time.Minutes:D2}:{time.Seconds:D2}";
            else
                return $"{time.Minutes}:{time.Seconds:D2}";
        }
        
        /// <summary>
        /// Format time for filename: HHhMMmSSs
        /// تنسيق الوقت لاسم الملف: HHhMMmSSs
        /// </summary>
        public static string FormatTimeFilename(TimeSpan time)
        {
            if (time < TimeSpan.Zero)
                time = TimeSpan.Zero;
                
            return $"{time.Hours:D2}h{time.Minutes:D2}m{time.Seconds:D2}s";
        }
        
        #endregion
        
        #region Time Parsing (تحليل الوقت)
        
        /// <summary>
        /// Parse time from various formats
        /// تحليل الوقت من صيغ مختلفة
        /// </summary>
        public static bool TryParseTime(string input, out TimeSpan result)
        {
            result = TimeSpan.Zero;
            
            if (string.IsNullOrWhiteSpace(input))
                return false;
                
            // Remove extra spaces
            input = input.Trim();
            
            // Try standard TimeSpan parsing first
            if (TimeSpan.TryParse(input, out result))
                return true;
                
            // Try Bandicut format: H:MM:SS.FF
            var bandicutPattern = @"^(\d+):(\d{1,2}):(\d{1,2})\.(\d{1,2})$";
            var match = Regex.Match(input, bandicutPattern);
            if (match.Success)
            {
                if (int.TryParse(match.Groups[1].Value, out int hours) &&
                    int.TryParse(match.Groups[2].Value, out int minutes) &&
                    int.TryParse(match.Groups[3].Value, out int seconds) &&
                    int.TryParse(match.Groups[4].Value, out int centiseconds))
                {
                    if (IsValidTime(hours, minutes, seconds, centiseconds))
                    {
                        result = new TimeSpan(0, hours, minutes, seconds, centiseconds * 10);
                        return true;
                    }
                }
            }
            
            // Try MM:SS format
            var shortPattern = @"^(\d{1,2}):(\d{1,2})$";
            match = Regex.Match(input, shortPattern);
            if (match.Success)
            {
                if (int.TryParse(match.Groups[1].Value, out int minutes) &&
                    int.TryParse(match.Groups[2].Value, out int seconds))
                {
                    if (IsValidTime(0, minutes, seconds, 0))
                    {
                        result = new TimeSpan(0, 0, minutes, seconds, 0);
                        return true;
                    }
                }
            }
            
            // Try seconds only
            if (double.TryParse(input, NumberStyles.Float, CultureInfo.InvariantCulture, out double totalSeconds))
            {
                if (totalSeconds >= 0 && totalSeconds <= TimeSpan.MaxValue.TotalSeconds)
                {
                    result = TimeSpan.FromSeconds(totalSeconds);
                    return true;
                }
            }
            
            return false;
        }
        
        #endregion
        
        #region Time Validation (التحقق من صحة الوقت)
        
        /// <summary>
        /// Validate time components
        /// التحقق من صحة مكونات الوقت
        /// </summary>
        public static bool IsValidTime(int hours, int minutes, int seconds, int centiseconds)
        {
            return hours >= 0 && hours <= 23 &&
                   minutes >= 0 && minutes <= 59 &&
                   seconds >= 0 && seconds <= 59 &&
                   centiseconds >= 0 && centiseconds <= 99;
        }
        
        /// <summary>
        /// Validate time range for video cutting
        /// التحقق من صحة نطاق الوقت لقص الفيديو
        /// </summary>
        public static bool IsValidTimeRange(TimeSpan startTime, TimeSpan endTime, TimeSpan videoDuration)
        {
            return startTime >= TimeSpan.Zero &&
                   endTime > startTime &&
                   endTime <= videoDuration &&
                   (endTime - startTime).TotalMilliseconds >= 100; // Minimum 100ms duration
        }
        
        /// <summary>
        /// Clamp time to valid range
        /// تقييد الوقت إلى نطاق صحيح
        /// </summary>
        public static TimeSpan ClampTime(TimeSpan time, TimeSpan minTime, TimeSpan maxTime)
        {
            if (time < minTime) return minTime;
            if (time > maxTime) return maxTime;
            return time;
        }
        
        #endregion
        
        #region Time Calculations (حسابات الوقت)
        
        /// <summary>
        /// Calculate duration between two times
        /// حساب المدة بين وقتين
        /// </summary>
        public static TimeSpan CalculateDuration(TimeSpan startTime, TimeSpan endTime)
        {
            if (endTime <= startTime)
                return TimeSpan.Zero;
                
            return endTime - startTime;
        }
        
        /// <summary>
        /// Calculate percentage of time within duration
        /// حساب نسبة الوقت ضمن المدة
        /// </summary>
        public static double CalculateTimePercentage(TimeSpan currentTime, TimeSpan totalDuration)
        {
            if (totalDuration.TotalSeconds <= 0)
                return 0;
                
            var percentage = (currentTime.TotalSeconds / totalDuration.TotalSeconds) * 100;
            return Math.Max(0, Math.Min(100, percentage));
        }
        
        /// <summary>
        /// Calculate time from percentage
        /// حساب الوقت من النسبة المئوية
        /// </summary>
        public static TimeSpan CalculateTimeFromPercentage(double percentage, TimeSpan totalDuration)
        {
            percentage = Math.Max(0, Math.Min(100, percentage));
            var seconds = (percentage / 100.0) * totalDuration.TotalSeconds;
            return TimeSpan.FromSeconds(seconds);
        }
        
        /// <summary>
        /// Round time to nearest frame (assuming 30 FPS)
        /// تقريب الوقت إلى أقرب إطار (بافتراض 30 إطار/ثانية)
        /// </summary>
        public static TimeSpan RoundToNearestFrame(TimeSpan time, double frameRate = 30.0)
        {
            var frameTime = 1.0 / frameRate;
            var totalSeconds = time.TotalSeconds;
            var roundedSeconds = Math.Round(totalSeconds / frameTime) * frameTime;
            return TimeSpan.FromSeconds(roundedSeconds);
        }
        
        #endregion
        
        #region Time Comparison (مقارنة الأوقات)
        
        /// <summary>
        /// Compare times with tolerance
        /// مقارنة الأوقات مع هامش التسامح
        /// </summary>
        public static bool AreTimesEqual(TimeSpan time1, TimeSpan time2, TimeSpan tolerance)
        {
            var difference = Math.Abs((time1 - time2).TotalMilliseconds);
            return difference <= tolerance.TotalMilliseconds;
        }
        
        /// <summary>
        /// Check if time is within range
        /// التحقق من وجود الوقت ضمن النطاق
        /// </summary>
        public static bool IsTimeInRange(TimeSpan time, TimeSpan startRange, TimeSpan endRange)
        {
            return time >= startRange && time <= endRange;
        }
        
        #endregion
        
        #region Error Handling (معالجة الأخطاء)
        
        /// <summary>
        /// Get time validation error message
        /// الحصول على رسالة خطأ التحقق من الوقت
        /// </summary>
        public static string GetTimeValidationError(TimeSpan startTime, TimeSpan endTime, TimeSpan videoDuration)
        {
            if (startTime < TimeSpan.Zero)
                return "وقت البداية لا يمكن أن يكون سالباً | Start time cannot be negative";
                
            if (endTime <= startTime)
                return "وقت النهاية يجب أن يكون أكبر من وقت البداية | End time must be greater than start time";
                
            if (endTime > videoDuration)
                return "وقت النهاية يتجاوز مدة الفيديو | End time exceeds video duration";
                
            if ((endTime - startTime).TotalMilliseconds < 100)
                return "المدة قصيرة جداً (الحد الأدنى 100 مللي ثانية) | Duration too short (minimum 100ms)";
                
            return string.Empty;
        }
        
        #endregion
    }
}
