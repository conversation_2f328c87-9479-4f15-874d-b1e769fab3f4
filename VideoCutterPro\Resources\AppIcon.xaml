<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- App Icon as Vector -->
    <DrawingImage x:Key="AppIconDrawing">
        <DrawingImage.Drawing>
            <DrawingGroup>
                <!-- Background Circle -->
                <GeometryDrawing Brush="#7CB342">
                    <GeometryDrawing.Geometry>
                        <EllipseGeometry Center="32,32" RadiusX="30" RadiusY="30"/>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
                
                <!-- Video Camera Icon -->
                <GeometryDrawing Brush="White">
                    <GeometryDrawing.Geometry>
                        <PathGeometry>
                            <PathFigure StartPoint="16,20">
                                <LineSegment Point="40,20"/>
                                <LineSegment Point="40,44"/>
                                <LineSegment Point="16,44"/>
                                <LineSegment Point="16,20"/>
                            </PathFigure>
                        </PathGeometry>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
                
                <!-- Lens -->
                <GeometryDrawing Brush="#7CB342">
                    <GeometryDrawing.Geometry>
                        <EllipseGeometry Center="28,32" RadiusX="6" RadiusY="6"/>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
                
                <!-- Scissors (Cut Symbol) -->
                <GeometryDrawing Brush="White" Pen="{x:Null}">
                    <GeometryDrawing.Geometry>
                        <PathGeometry>
                            <PathFigure StartPoint="44,24">
                                <LineSegment Point="52,16"/>
                                <LineSegment Point="54,18"/>
                                <LineSegment Point="46,26"/>
                                <LineSegment Point="54,34"/>
                                <LineSegment Point="52,36"/>
                                <LineSegment Point="44,28"/>
                            </PathFigure>
                        </PathGeometry>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
                
                <!-- Cut Line -->
                <GeometryDrawing>
                    <GeometryDrawing.Pen>
                        <Pen Brush="White" Thickness="2"/>
                    </GeometryDrawing.Pen>
                    <GeometryDrawing.Geometry>
                        <LineGeometry StartPoint="42,26" EndPoint="48,32"/>
                    </GeometryDrawing.Geometry>
                </GeometryDrawing>
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>
    
</ResourceDictionary>
