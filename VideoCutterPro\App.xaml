﻿<Application x:Class="VideoCutterPro.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:VideoCutterPro"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
                <!-- Custom Styles -->
                    <!-- Converters -->
                    <local:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter"/>
                    <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    <local:FlowDirectionConverter x:Key="FlowDirectionConverter"/>
                    <local:DoubleToPercentageConverter x:Key="DoubleToPercentageConverter"/>
                    <!-- Custom Colors -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#673AB7"/>
                    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#9C27B0"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
                    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>

                    <!-- Dark Theme Colors -->
                    <SolidColorBrush x:Key="DarkBackgroundBrush" Color="#121212"/>
                    <SolidColorBrush x:Key="DarkSurfaceBrush" Color="#1E1E1E"/>
                    <SolidColorBrush x:Key="DarkTextPrimaryBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="DarkTextSecondaryBrush" Color="#B3B3B3"/>

                    <!-- Custom Button Style -->
                    <Style x:Key="ModernButtonStyle" TargetType="Button">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Cursor" Value="Hand"/>
                    </Style>

                    <!-- Start Button Style -->
                    <Style x:Key="StartButtonStyle" TargetType="Button">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Cursor" Value="Hand"/>
                    </Style>

                    <!-- Timeline Slider Style -->
                    <Style x:Key="TimelineSliderStyle" TargetType="Slider">
                        <Setter Property="Height" Value="20"/>
                        <Setter Property="Margin" Value="10,5"/>
                    </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
