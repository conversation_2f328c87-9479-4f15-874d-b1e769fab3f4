using System.Windows;
using MaterialDesignThemes.Wpf;

namespace VideoCutterPro.Services
{
    public class ThemeService
    {
        private static ThemeService? _instance;
        public static ThemeService Instance => _instance ??= new ThemeService();

        private readonly PaletteHelper _paletteHelper;

        private ThemeService()
        {
            _paletteHelper = new PaletteHelper();
        }

        public void ApplyTheme(string themeName)
        {
            try
            {
                var theme = _paletteHelper.GetTheme();
                
                if (themeName.ToLower() == "dark")
                {
                    theme.SetBaseTheme(BaseTheme.Dark);
                    UpdateApplicationResources(true);
                }
                else
                {
                    theme.SetBaseTheme(BaseTheme.Light);
                    UpdateApplicationResources(false);
                }

                // Set custom colors
                theme.SetPrimaryColor(System.Windows.Media.Color.FromRgb(103, 58, 183)); // Deep Purple
                theme.SetSecondaryColor(System.Windows.Media.Color.FromRgb(76, 175, 80)); // Green

                _paletteHelper.SetTheme(theme);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        private void UpdateApplicationResources(bool isDark)
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            try
            {
                if (isDark)
                {
                    // Dark theme colors
                    app.Resources["BackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(18, 18, 18));
                    app.Resources["SurfaceBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(30, 30, 30));
                    app.Resources["TextPrimaryBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(255, 255, 255));
                    app.Resources["TextSecondaryBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(179, 179, 179));
                }
                else
                {
                    // Light theme colors
                    app.Resources["BackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(250, 250, 250));
                    app.Resources["SurfaceBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(255, 255, 255));
                    app.Resources["TextPrimaryBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(33, 33, 33));
                    app.Resources["TextSecondaryBrush"] = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(117, 117, 117));
                }

                // Common colors
                app.Resources["PrimaryBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(103, 58, 183));
                app.Resources["PrimaryLightBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(156, 39, 176));
                app.Resources["AccentBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(76, 175, 80));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating application resources: {ex.Message}");
            }
        }

        public string GetCurrentTheme()
        {
            try
            {
                var theme = _paletteHelper.GetTheme();
                return theme.GetBaseTheme() == BaseTheme.Dark ? "Dark" : "Light";
            }
            catch
            {
                return "Light";
            }
        }
    }
}
