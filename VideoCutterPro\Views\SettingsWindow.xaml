<Window x:Class="VideoCutterPro.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VideoCutterPro.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{Binding LocalizationService.Settings}"
        Height="600" Width="500"
        MinHeight="500" MinWidth="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid>
        <Grid.RowDefinitions>
            <!-- Title Bar -->
            <RowDefinition Height="50"/>
            <!-- Content -->
            <RowDefinition Height="*"/>
            <!-- Buttons -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <materialDesign:PackIcon Kind="Settings" Foreground="White" Width="24" Height="24" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding LocalizationService.Settings}" 
                           Foreground="White" 
                           FontSize="16" 
                           FontWeight="Medium" 
                           VerticalAlignment="Center" 
                           Margin="10,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <TabControl Grid.Row="1" Margin="20" Style="{StaticResource MaterialDesignTabControl}">
            <!-- General Settings Tab -->
            <TabItem Header="{Binding LocalizationService.Settings}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <!-- Language Settings -->
                        <materialDesign:Card Margin="0,0,0,20">
                            <StackPanel Margin="20">
                                <TextBlock Text="{Binding LocalizationService.Language}" 
                                           FontSize="16" 
                                           FontWeight="Bold" 
                                           Margin="0,0,0,15"/>
                                
                                <ComboBox Name="LanguageComboBox"
                                          materialDesign:HintAssist.Hint="{Binding LocalizationService.Language}"
                                          Style="{StaticResource MaterialDesignComboBox}"
                                          SelectionChanged="LanguageComboBox_SelectionChanged">
                                    <ComboBoxItem Content="{Binding LocalizationService.Arabic}" Tag="ar"/>
                                    <ComboBoxItem Content="{Binding LocalizationService.English}" Tag="en"/>
                                </ComboBox>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <!-- Theme Settings -->
                        <materialDesign:Card Margin="0,0,0,20">
                            <StackPanel Margin="20">
                                <TextBlock Text="{Binding LocalizationService.Theme}"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="0,0,0,15"/>

                                <ComboBox Name="ThemeComboBox"
                                          materialDesign:HintAssist.Hint="{Binding LocalizationService.Theme}"
                                          Style="{StaticResource MaterialDesignComboBox}"
                                          SelectionChanged="ThemeComboBox_SelectionChanged">
                                    <ComboBoxItem Content="{Binding LocalizationService.Light}" Tag="Light"/>
                                    <ComboBoxItem Content="{Binding LocalizationService.Dark}" Tag="Dark"/>
                                </ComboBox>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <!-- Output Settings -->
                        <materialDesign:Card Margin="0,0,0,20">
                            <StackPanel Margin="20">
                                <TextBlock Text="{Binding LocalizationService.OutputDirectory}" 
                                           FontSize="16" 
                                           FontWeight="Bold" 
                                           Margin="0,0,0,15"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBox Grid.Column="0"
                                             Name="OutputDirectoryTextBox"
                                             materialDesign:HintAssist.Hint="{Binding LocalizationService.OutputDirectory}"
                                             Style="{StaticResource MaterialDesignTextBox}"
                                             IsReadOnly="True"/>
                                    
                                    <Button Grid.Column="1"
                                            Name="BrowseOutputDirectoryButton"
                                            Content="{Binding LocalizationService.Browse}"
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="10,0,0,0"
                                            Click="BrowseOutputDirectoryButton_Click"/>
                                </Grid>
                                
                                <TextBlock Text="{Binding LocalizationService.OutputFormat}" 
                                           FontSize="14" 
                                           FontWeight="Bold" 
                                           Margin="0,20,0,10"/>
                                
                                <ComboBox Name="OutputFormatComboBox"
                                          materialDesign:HintAssist.Hint="{Binding LocalizationService.OutputFormat}"
                                          Style="{StaticResource MaterialDesignComboBox}"
                                          SelectionChanged="OutputFormatComboBox_SelectionChanged">
                                    <ComboBoxItem Content="MP4" Tag="mp4"/>
                                    <ComboBoxItem Content="AVI" Tag="avi"/>
                                    <ComboBoxItem Content="MKV" Tag="mkv"/>
                                    <ComboBoxItem Content="MOV" Tag="mov"/>
                                    <ComboBoxItem Content="WMV" Tag="wmv"/>
                                    <ComboBoxItem Content="FLV" Tag="flv"/>
                                    <ComboBoxItem Content="WEBM" Tag="webm"/>
                                </ComboBox>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <!-- Behavior Settings -->
                        <materialDesign:Card Margin="0,0,0,20">
                            <StackPanel Margin="20">
                                <TextBlock Text="Behavior" 
                                           FontSize="16" 
                                           FontWeight="Bold" 
                                           Margin="0,0,0,15"/>
                                
                                <CheckBox Name="ShowCountdownCheckBox"
                                          Content="{Binding LocalizationService.ShowCountdown}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Margin="0,5"
                                          Checked="ShowCountdownCheckBox_Checked"
                                          Unchecked="ShowCountdownCheckBox_Unchecked"/>
                                
                                <CheckBox Name="AutoCloseCheckBox"
                                          Content="{Binding LocalizationService.AutoClose}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Margin="0,5"
                                          Checked="AutoCloseCheckBox_Checked"
                                          Unchecked="AutoCloseCheckBox_Unchecked"/>
                                
                                <CheckBox Name="NotificationSoundCheckBox"
                                          Content="{Binding LocalizationService.NotificationSound}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Margin="0,5"
                                          Checked="NotificationSoundCheckBox_Checked"
                                          Unchecked="NotificationSoundCheckBox_Unchecked"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- About Tab -->
            <TabItem Header="{Binding LocalizationService.About}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20" HorizontalAlignment="Center">
                        <materialDesign:Card Padding="40">
                            <StackPanel HorizontalAlignment="Center">
                                <!-- App Icon -->
                                <materialDesign:PackIcon Kind="VideoVintage" 
                                                         Width="80" 
                                                         Height="80" 
                                                         Foreground="{StaticResource PrimaryBrush}"
                                                         HorizontalAlignment="Center"
                                                         Margin="0,0,0,20"/>
                                
                                <!-- App Name -->
                                <TextBlock Text="Video Cutter Pro" 
                                           FontSize="24" 
                                           FontWeight="Bold" 
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>
                                
                                <!-- Version -->
                                <TextBlock Text="Version 1.0.0" 
                                           FontSize="16" 
                                           HorizontalAlignment="Center"
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           Margin="0,0,0,20"/>
                                
                                <!-- Description -->
                                <TextBlock Text="Professional Video Cutting Tool for Enterprises" 
                                           FontSize="14" 
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           TextWrapping="Wrap"
                                           Margin="0,0,0,20"/>
                                
                                <!-- Developer Info -->
                                <TextBlock Text="Developed by Video Cutter Pro Team" 
                                           FontSize="12" 
                                           HorizontalAlignment="Center"
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           Margin="0,0,0,5"/>
                                
                                <!-- Copyright -->
                                <TextBlock Text="Copyright © 2025 Video Cutter Pro. All rights reserved." 
                                           FontSize="12" 
                                           HorizontalAlignment="Center"
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           Margin="0,0,0,20"/>
                                
                                <!-- Features -->
                                <TextBlock Text="Features:" 
                                           FontSize="14" 
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>
                                
                                <StackPanel HorizontalAlignment="Left">
                                    <TextBlock Text="• High-quality video cutting with FFmpeg" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Support for multiple video formats" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Drag and drop functionality" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Real-time preview and timeline control" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Multi-language support (Arabic/English)" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Dark and light theme support" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Progress tracking and notifications" FontSize="12" Margin="0,2"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" BorderBrush="{StaticResource TextSecondaryBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20">
                <Button Name="SaveButton"
                        Content="{Binding LocalizationService.Save}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Margin="0,0,10,0"
                        Click="SaveButton_Click"/>
                
                <Button Name="CancelButton"
                        Content="{Binding LocalizationService.Cancel}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
