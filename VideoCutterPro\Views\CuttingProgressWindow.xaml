<Window x:Class="VideoCutterPro.Views.CuttingProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VideoCutterPro.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{Binding LocalizationService.CuttingInProgress}"
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../App.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Border BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="2" CornerRadius="10">
        <Grid>
            <Grid.RowDefinitions>
                <!-- Title -->
                <RowDefinition Height="60"/>
                <!-- Content -->
                <RowDefinition Height="*"/>
                <!-- Buttons -->
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>
            
            <!-- Title Bar -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" CornerRadius="8,8,0,0">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <materialDesign:PackIcon Kind="ContentCut" Foreground="White" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding LocalizationService.CuttingInProgress}" 
                               Foreground="White" 
                               FontSize="16" 
                               FontWeight="Medium" 
                               VerticalAlignment="Center" 
                               Margin="10,0,0,0"/>
                </StackPanel>
            </Border>
            
            <!-- Content -->
            <StackPanel Grid.Row="1" Margin="40" VerticalAlignment="Center">
                <!-- Circular Progress -->
                <Grid HorizontalAlignment="Center" Margin="0,0,0,30">
                    <materialDesign:Card Width="120" Height="120">
                        <Grid>
                            <!-- Background Circle -->
                            <Ellipse Width="100" Height="100" 
                                     Stroke="{StaticResource TextSecondaryBrush}" 
                                     StrokeThickness="8" 
                                     Opacity="0.3"/>
                            
                            <!-- Progress Circle -->
                            <ProgressBar Name="ProgressCircle"
                                         Width="100"
                                         Height="100"
                                         Value="{Binding Progress}"
                                         Style="{StaticResource MaterialDesignCircularProgressBar}"
                                         Foreground="{StaticResource AccentBrush}"/>
                            
                            <!-- Progress Text -->
                            <TextBlock Text="{Binding Progress, StringFormat={}{0:F1}%}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="{StaticResource AccentBrush}"/>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
                
                <!-- Status Text -->
                <TextBlock Name="StatusTextBlock"
                           Text="{Binding Status}"
                           HorizontalAlignment="Center"
                           FontSize="14"
                           FontWeight="Medium"
                           Margin="0,0,0,15"/>
                
                <!-- Time Remaining -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15"
                            Visibility="{Binding ShowCountdown, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="Clock" Width="16" Height="16" VerticalAlignment="Center" Foreground="{StaticResource TextSecondaryBrush}"/>
                    <TextBlock Text="{Binding LocalizationService.TimeRemaining}" 
                               FontSize="12" 
                               Foreground="{StaticResource TextSecondaryBrush}"
                               Margin="5,0,5,0"/>
                    <TextBlock Text="{Binding TimeRemaining, Converter={StaticResource TimeSpanToStringConverter}}"
                               FontSize="12"
                               FontWeight="Bold"/>
                </StackPanel>
                
                <!-- File Info -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Output File:" FontSize="12" Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding OutputFileName}" 
                                   FontSize="14" 
                                   FontWeight="Bold"
                                   TextTrimming="CharacterEllipsis"
                                   ToolTip="{Binding OutputPath}"/>
                        
                        <TextBlock Text="Cut Duration:" FontSize="12" Foreground="{StaticResource TextSecondaryBrush}" Margin="0,10,0,0"/>
                        <TextBlock Text="{Binding CutDuration, Converter={StaticResource TimeSpanToStringConverter}}" 
                                   FontSize="14" 
                                   FontWeight="Bold"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
            
            <!-- Action Buttons -->
            <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" CornerRadius="0,0,8,8">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="20">
                    <Button Name="CancelButton"
                            Content="{Binding LocalizationService.Cancel}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="CancelButton_Click"
                            IsEnabled="{Binding CanCancel}">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding}" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
