using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using VideoCutterPro.Services;

namespace VideoCutterPro.Views;

public partial class SettingsWindow : Window, INotifyPropertyChanged
{
    public LocalizationService LocalizationService { get; set; }
    public SettingsService SettingsService { get; set; }

    private string _tempLanguage = "ar";
    private string _tempTheme = "Light";
    private string _tempOutputDirectory = "";
    private string _tempOutputFormat = "mp4";
    private bool _tempShowCountdown;
    private bool _tempAutoClose;
    private bool _tempNotificationSound;

    public SettingsWindow()
    {
        InitializeComponent();
        
        LocalizationService = LocalizationService.Instance;
        SettingsService = SettingsService.Instance;
        
        DataContext = this;
        
        LoadCurrentSettings();
    }

    private void LoadCurrentSettings()
    {
        var settings = SettingsService.Settings;
        
        // Store temporary values
        _tempLanguage = settings.Language;
        _tempTheme = settings.Theme;
        _tempOutputDirectory = settings.OutputDirectory;
        _tempOutputFormat = settings.DefaultOutputFormat;
        _tempShowCountdown = settings.ShowCountdown;
        _tempAutoClose = settings.AutoCloseAfterCutting;
        _tempNotificationSound = settings.PlayNotificationSound;
        
        // Set UI controls
        SetComboBoxSelection(LanguageComboBox, _tempLanguage);
        SetComboBoxSelection(ThemeComboBox, _tempTheme);
        OutputDirectoryTextBox.Text = _tempOutputDirectory;
        SetComboBoxSelection(OutputFormatComboBox, _tempOutputFormat);
        ShowCountdownCheckBox.IsChecked = _tempShowCountdown;
        AutoCloseCheckBox.IsChecked = _tempAutoClose;
        NotificationSoundCheckBox.IsChecked = _tempNotificationSound;
    }

    private void SetComboBoxSelection(ComboBox comboBox, string value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Tag?.ToString() == value)
            {
                comboBox.SelectedItem = item;
                break;
            }
        }
    }

    private string GetComboBoxSelection(ComboBox comboBox)
    {
        return (comboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "";
    }

    // Event Handlers
    private void LanguageComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _tempLanguage = GetComboBoxSelection(LanguageComboBox);
    }

    private void ThemeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _tempTheme = GetComboBoxSelection(ThemeComboBox);
        // Apply theme immediately for preview
        if (!string.IsNullOrEmpty(_tempTheme))
        {
            ThemeService.Instance.ApplyTheme(_tempTheme);
        }
    }

    private void BrowseOutputDirectoryButton_Click(object sender, RoutedEventArgs e)
    {
        var folderDialog = new OpenFileDialog
        {
            Title = LocalizationService.GetString("OutputDirectory"),
            CheckFileExists = false,
            CheckPathExists = true,
            FileName = "Select Folder",
            Filter = "Folders|*.folder",
            ValidateNames = false
        };

        if (folderDialog.ShowDialog() == true)
        {
            var selectedPath = System.IO.Path.GetDirectoryName(folderDialog.FileName);
            if (!string.IsNullOrEmpty(selectedPath))
            {
                _tempOutputDirectory = selectedPath;
                OutputDirectoryTextBox.Text = _tempOutputDirectory;
            }
        }
    }

    private void OutputFormatComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _tempOutputFormat = GetComboBoxSelection(OutputFormatComboBox);
    }

    private void ShowCountdownCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        _tempShowCountdown = true;
    }

    private void ShowCountdownCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        _tempShowCountdown = false;
    }

    private void AutoCloseCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        _tempAutoClose = true;
    }

    private void AutoCloseCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        _tempAutoClose = false;
    }

    private void NotificationSoundCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        _tempNotificationSound = true;
    }

    private void NotificationSoundCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        _tempNotificationSound = false;
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Validate output directory
            if (!System.IO.Directory.Exists(_tempOutputDirectory))
            {
                MessageBox.Show("Invalid output directory. Please select a valid directory.", 
                               "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Save settings
            var settings = SettingsService.Settings;
            settings.Language = _tempLanguage;
            settings.Theme = _tempTheme;
            settings.OutputDirectory = _tempOutputDirectory;
            settings.DefaultOutputFormat = _tempOutputFormat;
            settings.ShowCountdown = _tempShowCountdown;
            settings.AutoCloseAfterCutting = _tempAutoClose;
            settings.PlayNotificationSound = _tempNotificationSound;
            
            SettingsService.SaveSettings();
            
            // Apply language change
            LocalizationService.CurrentLanguage = _tempLanguage;
            
            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving settings: {ex.Message}", 
                           "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    // INotifyPropertyChanged Implementation
    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
