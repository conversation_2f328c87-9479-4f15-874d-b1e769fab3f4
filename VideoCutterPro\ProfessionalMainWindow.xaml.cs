using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;
using VideoCutterPro.Services;

namespace VideoCutterPro;

public partial class ProfessionalMainWindow : Window
{
    private DispatcherTimer _timer;
    private bool _isPlaying = false;
    private TimeSpan _videoDuration = TimeSpan.Zero;
    private TimeSpan _currentPosition = TimeSpan.Zero;
    private TimeSpan _startTime = TimeSpan.Zero;
    private TimeSpan _endTime = TimeSpan.Zero;

    public ProfessionalMainWindow()
    {
        InitializeComponent();
        
        // Initialize timer
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromMilliseconds(100);
        _timer.Tick += Timer_Tick;
        
        // Setup window
        SetupWindow();
        
        // Setup drag and drop
        AllowDrop = true;
        Drop += MainWindow_Drop;
        DragEnter += MainWindow_DragEnter;
        DragOver += MainWindow_DragOver;
        
        // Initialize UI
        UpdateTimeDisplay();
        UpdateDurationLabel();

        // Setup time input handlers
        SetupTimeInputHandlers();
    }

    private void SetupWindow()
    {
        WindowStartupLocation = WindowStartupLocation.CenterScreen;
        WindowState = WindowState.Normal;

        // Enable FULL resizing capabilities from ALL sides
        ResizeMode = ResizeMode.CanResizeWithGrip;
        AllowsTransparency = true;

        // Set default size as requested
        Width = 908;
        Height = 625;
        MinWidth = 850;
        MinHeight = 600;

        // Ensure window can be resized from all directions including top and bottom
        this.WindowStyle = WindowStyle.None;
        this.AllowsTransparency = true;

        // Ensure window can be resized from all directions
        this.SourceInitialized += (s, e) =>
        {
            var hwnd = new System.Windows.Interop.WindowInteropHelper(this).Handle;
            // Window will automatically support resize from all sides with CanResizeWithGrip
        };

        // Make window draggable from title bar
        var titleBar = FindName("TitleBar") as Border;
        if (titleBar != null)
        {
            titleBar.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    MaximizeButton_Click(s, e);
                }
                else if (e.LeftButton == MouseButtonState.Pressed)
                {
                    try
                    {
                        DragMove();
                    }
                    catch { /* Ignore drag errors */ }
                }
            };
        }

        // Add keyboard shortcuts for better control
        KeyDown += (s, e) =>
        {
            if (e.Key == Key.F11)
            {
                MaximizeButton_Click(s, new RoutedEventArgs());
            }
            else if (e.Key == Key.Escape && WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
                UpdateMaximizeButtonIcon();
            }
        };

        // Handle window state changes
        StateChanged += (s, e) => UpdateMaximizeButtonIcon();

        // Ensure visibility and focus
        Show();
        Activate();
        Focus();
        Focusable = true;

        // Update maximize button icon
        UpdateMaximizeButtonIcon();

        // Confirm resize capabilities
        this.ResizeMode = ResizeMode.CanResizeWithGrip;

        // Log window setup for debugging
        Console.WriteLine($"Window setup complete: {Width}x{Height}, ResizeMode: {ResizeMode}");
    }

    // Window Controls
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        UpdateMaximizeButtonIcon();
    }

    private void UpdateMaximizeButtonIcon()
    {
        var maximizeBtn = FindName("MaximizeBtn") as Button;
        if (maximizeBtn != null)
        {
            maximizeBtn.Content = WindowState == WindowState.Maximized ? "🗗" : "🗖";
            maximizeBtn.ToolTip = WindowState == WindowState.Maximized ? "استعادة | Restore" : "تكبير | Maximize";
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }



    // File Operations
    private void BrowseButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "اختيار ملف فيديو | Select Video File",
            Filter = "Video Files|*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm;*.m4v;*.3gp;*.mts;*.m2ts|All Files|*.*"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            LoadVideo(openFileDialog.FileName);
        }
    }

    private void LoadVideo(string filePath)
    {
        try
        {
            VideoPlayer.Source = new Uri(filePath);
            DragDropOverlay.Visibility = Visibility.Collapsed;

            // Get file info for Bandicut-style display
            var fileInfo = new System.IO.FileInfo(filePath);
            var fileName = System.IO.Path.GetFileName(filePath);

            // Update title bar with file info (Bandicut style)
            var titleText = FindName("TitleText") as TextBlock;
            var fileInfoText = FindName("FileInfoText") as TextBlock;
            var videoInfoText = FindName("VideoInfoText") as TextBlock;

            if (titleText != null)
                titleText.Text = "Video Cutter Pro";

            if (fileInfoText != null)
                fileInfoText.Text = fileName;

            if (videoInfoText != null)
                videoInfoText.Text = $"{fileName} (1280x720)"; // In real app, get actual dimensions

            // Simulate video duration (in real app, get from MediaElement)
            _videoDuration = TimeSpan.FromMinutes(0) + TimeSpan.FromSeconds(26) + TimeSpan.FromMilliseconds(50);
            _endTime = _videoDuration;

            // Update both timeline sliders
            var timelineSlider = FindName("TimelineSlider") as Slider;
            var mainTimelineSlider = FindName("MainTimelineSlider") as Slider;

            if (timelineSlider != null)
                timelineSlider.Maximum = _videoDuration.TotalSeconds;
            if (mainTimelineSlider != null)
                mainTimelineSlider.Maximum = _videoDuration.TotalSeconds;

            UpdateTimeDisplay();
            UpdateDurationLabel();
            UpdateEndTimeBox();

            MessageBox.Show($"تم تحميل الفيديو بنجاح!\nVideo loaded successfully!\n\nFile: {fileName}",
                           "نجح | Success", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الفيديو:\n{ex.Message}\n\nError loading video:\n{ex.Message}",
                           "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // Drag and Drop
    private void MainWindow_DragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            e.Effects = DragDropEffects.Copy;
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
    }

    private void MainWindow_DragOver(object sender, DragEventArgs e)
    {
        MainWindow_DragEnter(sender, e);
    }

    private void MainWindow_Drop(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files.Length > 0)
            {
                LoadVideo(files[0]);
            }
        }
    }

    // Playback Controls
    private void PlayPauseButton_Click(object sender, RoutedEventArgs e)
    {
        if (_isPlaying)
        {
            PauseVideo();
        }
        else
        {
            PlayVideo();
        }
    }

    private void PlayVideo()
    {
        _isPlaying = true;

        // Update both play buttons (main and black control bar)
        var playPauseButton = FindName("PlayPauseButton") as Button;
        var mainPlayPauseButton = FindName("MainPlayPauseButton") as Button;

        if (playPauseButton != null)
            playPauseButton.Content = "⏸";
        if (mainPlayPauseButton != null)
            mainPlayPauseButton.Content = "⏸";

        _timer.Start();
        VideoPlayer.Play();
    }

    private void PauseVideo()
    {
        _isPlaying = false;

        // Update both play buttons (main and black control bar)
        var playPauseButton = FindName("PlayPauseButton") as Button;
        var mainPlayPauseButton = FindName("MainPlayPauseButton") as Button;

        if (playPauseButton != null)
            playPauseButton.Content = "▶";
        if (mainPlayPauseButton != null)
            mainPlayPauseButton.Content = "▶";

        _timer.Stop();
        VideoPlayer.Pause();
    }

    // Timeline
    private void TimelineSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (!_timer.IsEnabled) // Only update if not playing
        {
            _currentPosition = TimeSpan.FromSeconds(e.NewValue);
            VideoPlayer.Position = _currentPosition;
            UpdateTimeDisplay();
            UpdateProgressTrack();

            // Sync both timeline sliders
            var timelineSlider = FindName("TimelineSlider") as Slider;
            var mainTimelineSlider = FindName("MainTimelineSlider") as Slider;

            if (sender == timelineSlider && mainTimelineSlider != null)
                mainTimelineSlider.Value = e.NewValue;
            else if (sender == mainTimelineSlider && timelineSlider != null)
                timelineSlider.Value = e.NewValue;
        }
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        if (_isPlaying && VideoPlayer.Source != null)
        {
            _currentPosition = VideoPlayer.Position;

            // Update both timeline sliders
            var timelineSlider = FindName("TimelineSlider") as Slider;
            var mainTimelineSlider = FindName("MainTimelineSlider") as Slider;

            if (timelineSlider != null)
                timelineSlider.Value = _currentPosition.TotalSeconds;
            if (mainTimelineSlider != null)
                mainTimelineSlider.Value = _currentPosition.TotalSeconds;

            UpdateTimeDisplay();
            UpdateProgressTrack();

            // Auto pause at end time
            if (_currentPosition >= _endTime)
            {
                PauseVideo();
            }
        }
    }

    // Cut Controls
    private void SetStartButton_Click(object sender, RoutedEventArgs e)
    {
        // Use advanced time service for precise time setting
        _startTime = AdvancedTimeService.RoundToNearestFrame(_currentPosition);
        _startTime = AdvancedTimeService.ClampTime(_startTime, TimeSpan.Zero, _videoDuration);

        StartTimeBox.Text = AdvancedTimeService.FormatTimeBandicut(_startTime);
        UpdateDurationLabel();
        ValidateTimeRange();
    }

    private void SetEndButton_Click(object sender, RoutedEventArgs e)
    {
        // Use advanced time service for precise time setting
        _endTime = AdvancedTimeService.RoundToNearestFrame(_currentPosition);
        _endTime = AdvancedTimeService.ClampTime(_endTime, _startTime, _videoDuration);

        EndTimeBox.Text = AdvancedTimeService.FormatTimeBandicut(_endTime);
        UpdateDurationLabel();
        ValidateTimeRange();
    }

    private void StartButton_Click(object sender, RoutedEventArgs e)
    {
        if (_videoDuration == TimeSpan.Zero)
        {
            MessageBox.Show("يرجى تحميل ملف فيديو أولاً\nPlease load a video file first",
                           "تحذير | Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Use advanced time validation
        var validationError = AdvancedTimeService.GetTimeValidationError(_startTime, _endTime, _videoDuration);
        if (!string.IsNullOrEmpty(validationError))
        {
            MessageBox.Show(validationError, "خطأ في الوقت | Time Error",
                           MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        var duration = AdvancedTimeService.CalculateDuration(_startTime, _endTime);
        var message = $@"سيتم قص الفيديو:
من: {AdvancedTimeService.FormatTimeBandicut(_startTime)}
إلى: {AdvancedTimeService.FormatTimeBandicut(_endTime)}
المدة: {AdvancedTimeService.FormatTimeBandicut(duration)}

Video will be cut:
From: {AdvancedTimeService.FormatTimeBandicut(_startTime)}
To: {AdvancedTimeService.FormatTimeBandicut(_endTime)}
Duration: {AdvancedTimeService.FormatTimeBandicut(duration)}

هل تريد المتابعة؟
Do you want to continue?";

        var result = MessageBox.Show(message, "تأكيد القص | Confirm Cut",
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            StartCutting();
        }
    }

    private void StartCutting()
    {
        MessageBox.Show("سيتم بدء عملية القص قريباً!\nCutting process will start soon!", 
                       "قريباً | Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // Helper Methods
    private void UpdateTimeDisplay()
    {
        // Update both time displays (main and black control bar) using advanced formatting
        var timeDisplay = FindName("TimeDisplay") as TextBlock;
        var videoTimeDisplay = FindName("VideoTimeDisplay") as TextBlock;

        var currentTimeText = AdvancedTimeService.FormatTimeBandicut(_currentPosition);
        var durationText = AdvancedTimeService.FormatTimeBandicut(_videoDuration);
        var timeText = $"{currentTimeText} / {durationText}";

        if (timeDisplay != null)
            timeDisplay.Text = timeText;
        if (videoTimeDisplay != null)
            videoTimeDisplay.Text = timeText;
    }

    private void UpdateDurationLabel()
    {
        var duration = AdvancedTimeService.CalculateDuration(_startTime, _endTime);
        var durationLabel = FindName("DurationLabel") as TextBlock;
        if (durationLabel != null)
            durationLabel.Text = $"Duration {AdvancedTimeService.FormatTimeBandicut(duration)}";
    }

    private void UpdateEndTimeBox()
    {
        var endTimeBox = FindName("EndTimeBox") as TextBox;
        if (endTimeBox != null)
            endTimeBox.Text = AdvancedTimeService.FormatTimeBandicut(_endTime);
    }

    private void UpdateProgressTrack()
    {
        if (_videoDuration.TotalSeconds > 0)
        {
            var percentage = AdvancedTimeService.CalculateTimePercentage(_currentPosition, _videoDuration) / 100.0;

            // Update both progress tracks
            var progressTrack = FindName("ProgressTrack") as System.Windows.Shapes.Rectangle;
            var mainProgressTrack = FindName("MainProgressTrack") as System.Windows.Shapes.Rectangle;
            var timelineSlider = FindName("TimelineSlider") as Slider;
            var mainTimelineSlider = FindName("MainTimelineSlider") as Slider;

            if (progressTrack != null && timelineSlider != null)
                progressTrack.Width = timelineSlider.ActualWidth * percentage;
            if (mainProgressTrack != null && mainTimelineSlider != null)
                mainProgressTrack.Width = mainTimelineSlider.ActualWidth * percentage;
        }
    }

    private void ValidateTimeRange()
    {
        // Visual feedback for time validation
        var startTimeBox = FindName("StartTimeBox") as TextBox;
        var endTimeBox = FindName("EndTimeBox") as TextBox;

        var isValid = AdvancedTimeService.IsValidTimeRange(_startTime, _endTime, _videoDuration);

        if (startTimeBox != null)
            startTimeBox.Background = isValid ? System.Windows.Media.Brushes.White : System.Windows.Media.Brushes.LightPink;
        if (endTimeBox != null)
            endTimeBox.Background = isValid ? System.Windows.Media.Brushes.White : System.Windows.Media.Brushes.LightPink;
    }

    private string FormatTime(TimeSpan time)
    {
        return AdvancedTimeService.FormatTimeBandicut(time);
    }

    #region Advanced Time Input Handling (معالجة إدخال الوقت المتقدمة)

    private void SetupTimeInputHandlers()
    {
        var startTimeBox = FindName("StartTimeBox") as TextBox;
        var endTimeBox = FindName("EndTimeBox") as TextBox;

        if (startTimeBox != null)
        {
            startTimeBox.LostFocus += StartTimeBox_LostFocus;
            startTimeBox.KeyDown += TimeBox_KeyDown;
        }

        if (endTimeBox != null)
        {
            endTimeBox.LostFocus += EndTimeBox_LostFocus;
            endTimeBox.KeyDown += TimeBox_KeyDown;
        }
    }

    private void StartTimeBox_LostFocus(object sender, RoutedEventArgs e)
    {
        var textBox = sender as TextBox;
        if (textBox != null)
        {
            if (AdvancedTimeService.TryParseTime(textBox.Text, out TimeSpan parsedTime))
            {
                _startTime = AdvancedTimeService.ClampTime(parsedTime, TimeSpan.Zero, _videoDuration);
                textBox.Text = AdvancedTimeService.FormatTimeBandicut(_startTime);
                UpdateDurationLabel();
                ValidateTimeRange();
            }
            else
            {
                // Restore previous valid value
                textBox.Text = AdvancedTimeService.FormatTimeBandicut(_startTime);
                MessageBox.Show("صيغة الوقت غير صحيحة. استخدم: H:MM:SS.FF\nInvalid time format. Use: H:MM:SS.FF",
                               "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }

    private void EndTimeBox_LostFocus(object sender, RoutedEventArgs e)
    {
        var textBox = sender as TextBox;
        if (textBox != null)
        {
            if (AdvancedTimeService.TryParseTime(textBox.Text, out TimeSpan parsedTime))
            {
                _endTime = AdvancedTimeService.ClampTime(parsedTime, _startTime, _videoDuration);
                textBox.Text = AdvancedTimeService.FormatTimeBandicut(_endTime);
                UpdateDurationLabel();
                ValidateTimeRange();
            }
            else
            {
                // Restore previous valid value
                textBox.Text = AdvancedTimeService.FormatTimeBandicut(_endTime);
                MessageBox.Show("صيغة الوقت غير صحيحة. استخدم: H:MM:SS.FF\nInvalid time format. Use: H:MM:SS.FF",
                               "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }

    private void TimeBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            // Move focus to trigger LostFocus event
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                textBox.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
            }
        }
    }

    #endregion
}
