using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;

namespace VideoCutterPro;

public partial class ProfessionalMainWindow : Window
{
    private DispatcherTimer _timer;
    private bool _isPlaying = false;
    private TimeSpan _videoDuration = TimeSpan.Zero;
    private TimeSpan _currentPosition = TimeSpan.Zero;
    private TimeSpan _startTime = TimeSpan.Zero;
    private TimeSpan _endTime = TimeSpan.Zero;

    public ProfessionalMainWindow()
    {
        InitializeComponent();
        
        // Initialize timer
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromMilliseconds(100);
        _timer.Tick += Timer_Tick;
        
        // Setup window
        SetupWindow();
        
        // Setup drag and drop
        AllowDrop = true;
        Drop += MainWindow_Drop;
        DragEnter += MainWindow_DragEnter;
        DragOver += MainWindow_DragOver;
        
        // Initialize UI
        UpdateTimeDisplay();
        UpdateDurationLabel();
    }

    private void SetupWindow()
    {
        WindowStartupLocation = WindowStartupLocation.CenterScreen;
        WindowState = WindowState.Normal;

        // Enable FULL resizing capabilities from ALL sides
        ResizeMode = ResizeMode.CanResizeWithGrip;
        AllowsTransparency = true;

        // Ensure window can be resized from all directions
        this.SourceInitialized += (s, e) =>
        {
            var hwnd = new System.Windows.Interop.WindowInteropHelper(this).Handle;
            // Enable resize from all sides programmatically if needed
        };

        // Make window draggable from title bar
        var titleBar = FindName("TitleBar") as Border;
        if (titleBar != null)
        {
            titleBar.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    MaximizeButton_Click(s, e);
                }
                else if (e.LeftButton == MouseButtonState.Pressed)
                {
                    try
                    {
                        DragMove();
                    }
                    catch { /* Ignore drag errors */ }
                }
            };
        }

        // Add keyboard shortcuts for better control
        KeyDown += (s, e) =>
        {
            if (e.Key == Key.F11)
            {
                MaximizeButton_Click(s, new RoutedEventArgs());
            }
            else if (e.Key == Key.Escape && WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
                UpdateMaximizeButtonIcon();
            }
        };

        // Handle window state changes
        StateChanged += (s, e) => UpdateMaximizeButtonIcon();

        // Ensure visibility and focus
        Show();
        Activate();
        Focus();
        Focusable = true;

        // Update maximize button icon
        UpdateMaximizeButtonIcon();

        // Force window to be resizable from all sides
        this.ResizeMode = ResizeMode.CanResizeWithGrip;
    }

    // Window Controls
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        UpdateMaximizeButtonIcon();
    }

    private void UpdateMaximizeButtonIcon()
    {
        var maximizeBtn = FindName("MaximizeBtn") as Button;
        if (maximizeBtn != null)
        {
            maximizeBtn.Content = WindowState == WindowState.Maximized ? "🗗" : "🗖";
            maximizeBtn.ToolTip = WindowState == WindowState.Maximized ? "استعادة | Restore" : "تكبير | Maximize";
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }



    // File Operations
    private void BrowseButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "اختيار ملف فيديو | Select Video File",
            Filter = "Video Files|*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm;*.m4v;*.3gp;*.mts;*.m2ts|All Files|*.*"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            LoadVideo(openFileDialog.FileName);
        }
    }

    private void LoadVideo(string filePath)
    {
        try
        {
            VideoPlayer.Source = new Uri(filePath);
            DragDropOverlay.Visibility = Visibility.Collapsed;
            
            // Simulate video duration (in real app, get from MediaElement)
            _videoDuration = TimeSpan.FromMinutes(11) + TimeSpan.FromSeconds(1) + TimeSpan.FromMilliseconds(810);
            _endTime = _videoDuration;
            
            TimelineSlider.Maximum = _videoDuration.TotalSeconds;
            
            UpdateTimeDisplay();
            UpdateDurationLabel();
            
            MessageBox.Show($"تم تحميل الفيديو بنجاح!\nVideo loaded successfully!\n\nFile: {System.IO.Path.GetFileName(filePath)}", 
                           "نجح | Success", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الفيديو:\n{ex.Message}\n\nError loading video:\n{ex.Message}", 
                           "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // Drag and Drop
    private void MainWindow_DragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            e.Effects = DragDropEffects.Copy;
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
    }

    private void MainWindow_DragOver(object sender, DragEventArgs e)
    {
        MainWindow_DragEnter(sender, e);
    }

    private void MainWindow_Drop(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files.Length > 0)
            {
                LoadVideo(files[0]);
            }
        }
    }

    // Playback Controls
    private void PlayPauseButton_Click(object sender, RoutedEventArgs e)
    {
        if (_isPlaying)
        {
            PauseVideo();
        }
        else
        {
            PlayVideo();
        }
    }

    private void PlayVideo()
    {
        _isPlaying = true;
        PlayPauseButton.Content = "⏸";
        _timer.Start();
        VideoPlayer.Play();
    }

    private void PauseVideo()
    {
        _isPlaying = false;
        PlayPauseButton.Content = "▶";
        _timer.Stop();
        VideoPlayer.Pause();
    }

    // Timeline
    private void TimelineSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (!_timer.IsEnabled) // Only update if not playing
        {
            _currentPosition = TimeSpan.FromSeconds(e.NewValue);
            VideoPlayer.Position = _currentPosition;
            UpdateTimeDisplay();
            UpdateProgressTrack();
        }
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        if (_isPlaying && VideoPlayer.Source != null)
        {
            _currentPosition = VideoPlayer.Position;
            TimelineSlider.Value = _currentPosition.TotalSeconds;
            UpdateTimeDisplay();
            UpdateProgressTrack();
            
            // Auto pause at end time
            if (_currentPosition >= _endTime)
            {
                PauseVideo();
            }
        }
    }

    // Cut Controls
    private void SetStartButton_Click(object sender, RoutedEventArgs e)
    {
        _startTime = _currentPosition;
        StartTimeBox.Text = FormatTime(_startTime);
        UpdateDurationLabel();
    }

    private void SetEndButton_Click(object sender, RoutedEventArgs e)
    {
        _endTime = _currentPosition;
        EndTimeBox.Text = FormatTime(_endTime);
        UpdateDurationLabel();
    }

    private void StartButton_Click(object sender, RoutedEventArgs e)
    {
        if (_videoDuration == TimeSpan.Zero)
        {
            MessageBox.Show("يرجى تحميل ملف فيديو أولاً\nPlease load a video file first", 
                           "تحذير | Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (_startTime >= _endTime)
        {
            MessageBox.Show("وقت البداية يجب أن يكون أقل من وقت النهاية\nStart time must be less than end time", 
                           "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        var duration = _endTime - _startTime;
        var message = $@"سيتم قص الفيديو:
من: {FormatTime(_startTime)}
إلى: {FormatTime(_endTime)}
المدة: {FormatTime(duration)}

Video will be cut:
From: {FormatTime(_startTime)}
To: {FormatTime(_endTime)}
Duration: {FormatTime(duration)}

هل تريد المتابعة؟
Do you want to continue?";

        var result = MessageBox.Show(message, "تأكيد القص | Confirm Cut", 
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            StartCutting();
        }
    }

    private void StartCutting()
    {
        MessageBox.Show("سيتم بدء عملية القص قريباً!\nCutting process will start soon!", 
                       "قريباً | Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // Helper Methods
    private void UpdateTimeDisplay()
    {
        TimeDisplay.Text = $"{FormatTime(_currentPosition)} / {FormatTime(_videoDuration)}";
    }

    private void UpdateDurationLabel()
    {
        var duration = _endTime - _startTime;
        DurationLabel.Text = $"Duration {FormatTime(duration)}";
    }

    private void UpdateProgressTrack()
    {
        if (_videoDuration.TotalSeconds > 0)
        {
            var percentage = _currentPosition.TotalSeconds / _videoDuration.TotalSeconds;
            ProgressTrack.Width = TimelineSlider.ActualWidth * percentage;
        }
    }

    private string FormatTime(TimeSpan time)
    {
        return $"{(int)time.TotalHours}:{time.Minutes:D2}:{time.Seconds:D2}.{time.Milliseconds / 10:D2}";
    }
}
