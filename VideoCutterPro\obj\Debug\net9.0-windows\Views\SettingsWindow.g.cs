﻿#pragma checksum "..\..\..\..\Views\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6FF66AD651E93C4C91A678A6079626947849BAE0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using VideoCutterPro.Views;


namespace VideoCutterPro.Views {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OutputDirectoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseOutputDirectoryButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OutputFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowCountdownCheckBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCloseCheckBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox NotificationSoundCheckBox;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VideoCutterPro;V1.0.0.0;component/views/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 62 "..\..\..\..\Views\SettingsWindow.xaml"
            this.LanguageComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LanguageComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 80 "..\..\..\..\Views\SettingsWindow.xaml"
            this.ThemeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ThemeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OutputDirectoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.BrowseOutputDirectoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\Views\SettingsWindow.xaml"
            this.BrowseOutputDirectoryButton.Click += new System.Windows.RoutedEventHandler(this.BrowseOutputDirectoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.OutputFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 123 "..\..\..\..\Views\SettingsWindow.xaml"
            this.OutputFormatComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.OutputFormatComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ShowCountdownCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 147 "..\..\..\..\Views\SettingsWindow.xaml"
            this.ShowCountdownCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ShowCountdownCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 148 "..\..\..\..\Views\SettingsWindow.xaml"
            this.ShowCountdownCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ShowCountdownCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AutoCloseCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 154 "..\..\..\..\Views\SettingsWindow.xaml"
            this.AutoCloseCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoCloseCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 155 "..\..\..\..\Views\SettingsWindow.xaml"
            this.AutoCloseCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoCloseCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NotificationSoundCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 161 "..\..\..\..\Views\SettingsWindow.xaml"
            this.NotificationSoundCheckBox.Checked += new System.Windows.RoutedEventHandler(this.NotificationSoundCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 162 "..\..\..\..\Views\SettingsWindow.xaml"
            this.NotificationSoundCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.NotificationSoundCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\..\Views\SettingsWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 254 "..\..\..\..\Views\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

