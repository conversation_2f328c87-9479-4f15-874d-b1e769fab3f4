using System.ComponentModel;
using System.IO;
using System.Windows;
using VideoCutterPro.Models;
using VideoCutterPro.Services;

namespace VideoCutterPro.Views;

public partial class CuttingProgressWindow : Window, INotifyPropertyChanged
{
    private double _progress = 0;
    private string _status = "Initializing...";
    private TimeSpan _timeRemaining = TimeSpan.Zero;
    private bool _canCancel = true;
    private bool _showCountdown = true;

    public LocalizationService LocalizationService { get; set; }
    public VideoInfo VideoInfo { get; set; }
    public string OutputPath { get; set; }
    public string OutputFileName { get; set; }
    public TimeSpan CutDuration { get; set; }

    public double Progress
    {
        get => _progress;
        set
        {
            _progress = value;
            OnPropertyChanged();
        }
    }

    public string Status
    {
        get => _status;
        set
        {
            _status = value;
            OnPropertyChanged();
        }
    }

    public TimeSpan TimeRemaining
    {
        get => _timeRemaining;
        set
        {
            _timeRemaining = value;
            OnPropertyChanged();
        }
    }

    public bool CanCancel
    {
        get => _canCancel;
        set
        {
            _canCancel = value;
            OnPropertyChanged();
        }
    }

    public bool ShowCountdown
    {
        get => _showCountdown;
        set
        {
            _showCountdown = value;
            OnPropertyChanged();
        }
    }

    public CuttingProgressWindow(VideoInfo videoInfo, string outputPath)
    {
        InitializeComponent();
        
        LocalizationService = LocalizationService.Instance;
        VideoInfo = videoInfo;
        OutputPath = outputPath;
        OutputFileName = Path.GetFileName(outputPath);
        CutDuration = videoInfo.CutDuration;
        ShowCountdown = SettingsService.Instance.Settings.ShowCountdown;
        
        DataContext = this;
        
        // Center the window
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        if (CanCancel)
        {
            var result = MessageBox.Show(
                "Are you sure you want to cancel the cutting process?",
                "Cancel Cutting",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }
    }

    public void UpdateProgress(double progress)
    {
        Dispatcher.Invoke(() =>
        {
            Progress = progress;
        });
    }

    public void UpdateStatus(string status)
    {
        Dispatcher.Invoke(() =>
        {
            Status = status;
        });
    }

    public void UpdateTimeRemaining(TimeSpan timeRemaining)
    {
        Dispatcher.Invoke(() =>
        {
            TimeRemaining = timeRemaining;
        });
    }

    public void SetCompleted(bool success)
    {
        Dispatcher.Invoke(() =>
        {
            CanCancel = false;
            Progress = 100;
            
            if (success)
            {
                Status = LocalizationService.GetString("CuttingCompleted");
                
                // Auto-close after a delay if setting is enabled
                if (SettingsService.Instance.Settings.AutoCloseAfterCutting)
                {
                    var timer = new System.Windows.Threading.DispatcherTimer
                    {
                        Interval = TimeSpan.FromSeconds(2)
                    };
                    timer.Tick += (s, e) =>
                    {
                        timer.Stop();
                        DialogResult = true;
                        Close();
                    };
                    timer.Start();
                }
                else
                {
                    // Change cancel button to close button
                    CancelButton.Content = LocalizationService.GetString("Close");
                    CanCancel = true;
                }
            }
            else
            {
                Status = LocalizationService.GetString("CuttingFailed");
                CancelButton.Content = LocalizationService.GetString("Close");
                CanCancel = true;
            }
        });
    }

    // INotifyPropertyChanged Implementation
    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
