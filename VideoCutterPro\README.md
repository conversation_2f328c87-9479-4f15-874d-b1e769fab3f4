# Video Cutter Pro

## نظرة عامة | Overview

**Video Cutter Pro** هو تطبيق احترافي لقص الفيديوهات مصمم خصيصاً للشركات والمؤسسات. يوفر التطبيق واجهة مستخدم عصرية وسهلة الاستخدام مع دعم كامل للغة العربية والإنجليزية.

**Video Cutter Pro** is a professional video cutting application designed specifically for enterprises and organizations. The application provides a modern and user-friendly interface with full support for Arabic and English languages.

## الميزات الرئيسية | Key Features

### 🎬 قص الفيديو عالي الجودة | High-Quality Video Cutting
- قص بدون فقدان الجودة باستخدام FFmpeg
- دعم صيغ متعددة: MP4, AVI, MKV, MOV, WMV, FLV, WEBM
- معاينة فورية للمقطع المحدد

### 🌍 دعم متعدد اللغات | Multi-Language Support
- واجهة باللغة العربية مع دعم RTL
- واجهة باللغة الإنجليزية
- إمكانية التبديل الفوري بين اللغات

### 🎨 تصميم عصري | Modern Design
- واجهة Material Design جذابة
- دعم الوضع الداكن والفاتح
- تصميم متجاوب وسهل الاستخدام

### ⚡ ميزات متقدمة | Advanced Features
- السحب والإفلات للملفات
- شريط زمني تفاعلي لتحديد نقاط القص
- عرض تقدم العملية مع العد التنازلي
- تنبيهات صوتية عند اكتمال العملية
- حفظ الإعدادات تلقائياً

## متطلبات النظام | System Requirements

- Windows 7 أو أحدث | Windows 7 or later
- .NET 8.0 Runtime
- 4 GB RAM (الحد الأدنى) | 4 GB RAM (minimum)
- 100 MB مساحة فارغة | 100 MB free disk space

## التثبيت | Installation

### من الكود المصدري | From Source Code

1. تأكد من تثبيت .NET 8.0 SDK
2. استنسخ المستودع:
```bash
git clone [repository-url]
cd VideoCutterPro
```

3. بناء المشروع:
```bash
dotnet build
```

4. تشغيل التطبيق:
```bash
dotnet run
```

## كيفية الاستخدام | How to Use

### 1. تحميل الفيديو | Loading Video
- اسحب وأفلت ملف الفيديو في النافذة الرئيسية
- أو انقر على زر "اختيار فيديو"

### 2. تحديد نقاط القص | Setting Cut Points
- استخدم الشريط الزمني للتنقل في الفيديو
- انقر "تحديد بداية القص" لتعيين نقطة البداية
- انقر "تحديد نهاية القص" لتعيين نقطة النهاية

### 3. بدء القص | Start Cutting
- انقر الزر الأخضر "بدء القص"
- راقب تقدم العملية في النافذة المنبثقة
- سيتم حفظ الملف في المجلد المحدد

## الإعدادات | Settings

### اللغة | Language
- العربية (افتراضي)
- English

### المظهر | Theme
- فاتح (افتراضي)
- داكن

### إعدادات الإخراج | Output Settings
- مجلد الحفظ
- صيغة الإخراج الافتراضية
- عرض العد التنازلي
- الإغلاق التلقائي بعد القص
- تفعيل الأصوات التنبيهية

## الصيغ المدعومة | Supported Formats

### الإدخال | Input
- MP4, AVI, MKV, MOV, WMV, FLV, WEBM
- M4V, 3GP, 3G2, MTS, M2TS, TS, VOB
- ASF, RM, RMVB, DIVX, XVID, F4V

### الإخراج | Output
- MP4 (افتراضي)
- AVI, MKV, MOV, WMV, FLV, WEBM

## التقنيات المستخدمة | Technologies Used

- **Framework**: WPF (.NET 8.0)
- **UI Library**: Material Design in XAML
- **Video Processing**: FFMpegCore
- **Architecture**: MVVM Pattern
- **Language**: C#

## المساهمة | Contributing

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم | Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني: <EMAIL>

## الإصدارات | Versions

### v1.0.0 (الحالي | Current)
- الإصدار الأولي مع جميع الميزات الأساسية
- دعم اللغة العربية والإنجليزية
- واجهة Material Design
- قص الفيديو عالي الجودة

---

**تم التطوير بواسطة فريق Video Cutter Pro | Developed by Video Cutter Pro Team**

© 2025 Video Cutter Pro. جميع الحقوق محفوظة | All rights reserved.
