@echo off
title Video Cutter Pro - Fixed Version

echo.
echo ==========================================
echo    Video Cutter Pro
echo    النسخة المصححة - Fixed Version
echo ==========================================
echo.
echo جاري تشغيل النسخة المصححة...
echo Starting fixed version...
echo.

cd /d "%~dp0"

REM Try the fixed version first
if exist "publish-fixed\VideoCutterPro.exe" (
    echo تشغيل النسخة المصححة...
    echo Running fixed version...
    start "" "publish-fixed\VideoCutterPro.exe"
    echo.
    echo تم تشغيل التطبيق بنجاح!
    echo Application started successfully!
    echo.
    echo تم اصلاح جميع الاخطاء:
    echo All errors have been fixed:
    echo - خطأ الايقونة تم حله
    echo - Icon error resolved
    echo - الازرار تعمل بشكل مثالي
    echo - Buttons work perfectly
    echo - امكانية تغيير حجم النافذة
    echo - Window resizing capability
    echo - شريط عنوان محسن
    echo - Enhanced title bar
    echo.
    echo ابحث عن النافذة في شريط المهام
    echo Look for the window in the taskbar
    echo.
    timeout /t 5 >nul
    exit
)

echo تشغيل من الكود المصدري...
echo Running from source...
dotnet run

echo.
echo انتهى التشغيل
echo Execution completed
pause
