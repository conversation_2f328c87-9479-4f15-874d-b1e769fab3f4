using System.Collections.Generic;
using System.ComponentModel;

namespace VideoCutterPro.Services
{
    public class LocalizationService : INotifyPropertyChanged
    {
        private static LocalizationService? _instance;
        private string _currentLanguage = "ar";
        private Dictionary<string, Dictionary<string, string>> _translations = new();

        public static LocalizationService Instance => _instance ??= new LocalizationService();

        public string CurrentLanguage
        {
            get => _currentLanguage;
            set
            {
                _currentLanguage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsRightToLeft));
                // Notify all translation properties
                foreach (var key in _translations["ar"].Keys)
                {
                    OnPropertyChanged(key);
                }
            }
        }

        public bool IsRightToLeft => CurrentLanguage == "ar";

        private LocalizationService()
        {
            InitializeTranslations();
        }

        private void InitializeTranslations()
        {
            _translations = new Dictionary<string, Dictionary<string, string>>
            {
                ["ar"] = new Dictionary<string, string>
                {
                    ["AppTitle"] = "Video Cutter Pro",
                    ["SelectVideo"] = "اختيار فيديو",
                    ["Play"] = "تشغيل",
                    ["Pause"] = "إيقاف مؤقت",
                    ["Stop"] = "إيقاف",
                    ["SetStartTime"] = "تحديد بداية القص",
                    ["SetEndTime"] = "تحديد نهاية القص",
                    ["StartCutting"] = "بدء القص",
                    ["Settings"] = "الإعدادات",
                    ["About"] = "حول البرنامج",
                    ["Language"] = "اللغة",
                    ["Theme"] = "المظهر",
                    ["OutputDirectory"] = "مجلد الحفظ",
                    ["OutputFormat"] = "صيغة الإخراج",
                    ["ShowCountdown"] = "عرض العد التنازلي",
                    ["AutoClose"] = "إغلاق تلقائي بعد القص",
                    ["NotificationSound"] = "صوت التنبيه",
                    ["Light"] = "فاتح",
                    ["Dark"] = "داكن",
                    ["Arabic"] = "العربية",
                    ["English"] = "English",
                    ["Browse"] = "تصفح",
                    ["Save"] = "حفظ",
                    ["Cancel"] = "إلغاء",
                    ["Close"] = "إغلاق",
                    ["Duration"] = "المدة",
                    ["StartTime"] = "وقت البداية",
                    ["EndTime"] = "وقت النهاية",
                    ["CutDuration"] = "مدة القص",
                    ["CurrentTime"] = "الوقت الحالي",
                    ["FileSize"] = "حجم الملف",
                    ["Resolution"] = "الدقة",
                    ["Format"] = "الصيغة",
                    ["Progress"] = "التقدم",
                    ["TimeRemaining"] = "الوقت المتبقي",
                    ["CuttingInProgress"] = "جاري القص...",
                    ["CuttingCompleted"] = "تم القص بنجاح!",
                    ["Error"] = "خطأ",
                    ["Success"] = "نجح",
                    ["Warning"] = "تحذير",
                    ["Information"] = "معلومات",
                    ["SelectVideoFile"] = "اختر ملف فيديو",
                    ["VideoFiles"] = "ملفات الفيديو",
                    ["AllFiles"] = "جميع الملفات",
                    ["InvalidVideoFile"] = "ملف فيديو غير صالح",
                    ["NoVideoSelected"] = "لم يتم اختيار فيديو",
                    ["InvalidTimeRange"] = "نطاق زمني غير صالح",
                    ["CuttingFailed"] = "فشل في القص",
                    ["OutputSaved"] = "تم حفظ الملف في:",
                    ["DragDropHint"] = "اسحب وأفلت ملف فيديو هنا أو انقر لاختيار ملف"
                },
                ["en"] = new Dictionary<string, string>
                {
                    ["AppTitle"] = "Video Cutter Pro",
                    ["SelectVideo"] = "Select Video",
                    ["Play"] = "Play",
                    ["Pause"] = "Pause",
                    ["Stop"] = "Stop",
                    ["SetStartTime"] = "Set Start Time",
                    ["SetEndTime"] = "Set End Time",
                    ["StartCutting"] = "Start Cutting",
                    ["Settings"] = "Settings",
                    ["About"] = "About",
                    ["Language"] = "Language",
                    ["Theme"] = "Theme",
                    ["OutputDirectory"] = "Output Directory",
                    ["OutputFormat"] = "Output Format",
                    ["ShowCountdown"] = "Show Countdown",
                    ["AutoClose"] = "Auto Close After Cutting",
                    ["NotificationSound"] = "Notification Sound",
                    ["Light"] = "Light",
                    ["Dark"] = "Dark",
                    ["Arabic"] = "العربية",
                    ["English"] = "English",
                    ["Browse"] = "Browse",
                    ["Save"] = "Save",
                    ["Cancel"] = "Cancel",
                    ["Close"] = "Close",
                    ["Duration"] = "Duration",
                    ["StartTime"] = "Start Time",
                    ["EndTime"] = "End Time",
                    ["CutDuration"] = "Cut Duration",
                    ["CurrentTime"] = "Current Time",
                    ["FileSize"] = "File Size",
                    ["Resolution"] = "Resolution",
                    ["Format"] = "Format",
                    ["Progress"] = "Progress",
                    ["TimeRemaining"] = "Time Remaining",
                    ["CuttingInProgress"] = "Cutting in progress...",
                    ["CuttingCompleted"] = "Cutting completed successfully!",
                    ["Error"] = "Error",
                    ["Success"] = "Success",
                    ["Warning"] = "Warning",
                    ["Information"] = "Information",
                    ["SelectVideoFile"] = "Select Video File",
                    ["VideoFiles"] = "Video Files",
                    ["AllFiles"] = "All Files",
                    ["InvalidVideoFile"] = "Invalid video file",
                    ["NoVideoSelected"] = "No video selected",
                    ["InvalidTimeRange"] = "Invalid time range",
                    ["CuttingFailed"] = "Cutting failed",
                    ["OutputSaved"] = "File saved to:",
                    ["DragDropHint"] = "Drag and drop a video file here or click to select a file"
                }
            };
        }

        public string GetString(string key)
        {
            if (_translations.ContainsKey(CurrentLanguage) && _translations[CurrentLanguage].ContainsKey(key))
            {
                return _translations[CurrentLanguage][key];
            }
            return key; // Return key if translation not found
        }

        // Properties for binding
        public string AppTitle => GetString("AppTitle");
        public string SelectVideo => GetString("SelectVideo");
        public string Play => GetString("Play");
        public string Pause => GetString("Pause");
        public string Stop => GetString("Stop");
        public string SetStartTime => GetString("SetStartTime");
        public string SetEndTime => GetString("SetEndTime");
        public string StartCutting => GetString("StartCutting");
        public string Settings => GetString("Settings");
        public string About => GetString("About");
        public string Language => GetString("Language");
        public string Theme => GetString("Theme");
        public string OutputDirectory => GetString("OutputDirectory");
        public string OutputFormat => GetString("OutputFormat");
        public string ShowCountdown => GetString("ShowCountdown");
        public string AutoClose => GetString("AutoClose");
        public string NotificationSound => GetString("NotificationSound");
        public string Light => GetString("Light");
        public string Dark => GetString("Dark");
        public string Arabic => GetString("Arabic");
        public string English => GetString("English");
        public string Browse => GetString("Browse");
        public string Save => GetString("Save");
        public string Cancel => GetString("Cancel");
        public string Close => GetString("Close");
        public string Duration => GetString("Duration");
        public string StartTime => GetString("StartTime");
        public string EndTime => GetString("EndTime");
        public string CutDuration => GetString("CutDuration");
        public string CurrentTime => GetString("CurrentTime");
        public string FileSize => GetString("FileSize");
        public string Resolution => GetString("Resolution");
        public string Format => GetString("Format");
        public string Progress => GetString("Progress");
        public string TimeRemaining => GetString("TimeRemaining");
        public string CuttingInProgress => GetString("CuttingInProgress");
        public string CuttingCompleted => GetString("CuttingCompleted");
        public string Error => GetString("Error");
        public string Success => GetString("Success");
        public string Warning => GetString("Warning");
        public string Information => GetString("Information");
        public string DragDropHint => GetString("DragDropHint");

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
