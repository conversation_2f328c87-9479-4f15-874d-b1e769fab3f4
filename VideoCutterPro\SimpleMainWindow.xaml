<Window x:Class="VideoCutterPro.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Video Cutter Pro - قاطع الفيديو الاحترافي"
        Height="600" Width="800"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="100"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#673AB7" CornerRadius="5,5,0,0">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🎬" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="Video Cutter Pro - قاطع الفيديو الاحترافي" 
                           Foreground="White" 
                           FontSize="18" 
                           FontWeight="Bold" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Main Content -->
        <Border Grid.Row="1" Background="White" Margin="10" CornerRadius="5" 
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <!-- Video Area -->
                <Border Background="Black" CornerRadius="5" Margin="20">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="🎥" FontSize="80" Foreground="Gray" HorizontalAlignment="Center"/>
                        <TextBlock Text="اسحب ملف فيديو هنا أو انقر لاختيار ملف" 
                                   FontSize="16" 
                                   Foreground="Gray" 
                                   HorizontalAlignment="Center"
                                   Margin="0,20,0,0"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Drag video file here or click to select" 
                                   FontSize="14" 
                                   Foreground="LightGray" 
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,0"
                                   TextAlignment="Center"/>
                        
                        <Button Name="SelectVideoButton"
                                Content="اختيار فيديو | Select Video"
                                Background="#673AB7"
                                Foreground="White"
                                FontSize="14"
                                FontWeight="Bold"
                                Padding="20,10"
                                Margin="0,20,0,0"
                                BorderThickness="0"
                                Cursor="Hand"
                                Click="SelectVideoButton_Click"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        
        <!-- Controls -->
        <Border Grid.Row="2" Background="White" Margin="10,0,10,10" CornerRadius="5"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Left Controls -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="▶️ تشغيل" 
                            Background="#4CAF50" 
                            Foreground="White"
                            Padding="15,8" 
                            Margin="0,0,10,0"
                            BorderThickness="0"
                            FontWeight="Bold"
                            Cursor="Hand"/>
                    <Button Content="⏸️ إيقاف" 
                            Background="#FF9800" 
                            Foreground="White"
                            Padding="15,8"
                            BorderThickness="0"
                            FontWeight="Bold"
                            Cursor="Hand"/>
                </StackPanel>
                
                <!-- Center - Start Button -->
                <Button Grid.Column="1"
                        Content="🎬 بدء القص | Start Cutting"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        Padding="30,15"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="StartCuttingButton_Click"/>
                
                <!-- Right Controls -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right">
                    <Button Content="⚙️ إعدادات" 
                            Background="#9C27B0" 
                            Foreground="White"
                            Padding="15,8" 
                            Margin="10,0,0,0"
                            BorderThickness="0"
                            FontWeight="Bold"
                            Cursor="Hand"
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
