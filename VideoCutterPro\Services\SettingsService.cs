using VideoCutterPro.Models;

namespace VideoCutterPro.Services
{
    public class SettingsService
    {
        private static SettingsService? _instance;
        private AppSettings _settings;

        public static SettingsService Instance => _instance ??= new SettingsService();

        public AppSettings Settings => _settings;

        private SettingsService()
        {
            _settings = AppSettings.LoadSettings();
        }

        public void SaveSettings()
        {
            _settings.SaveSettings();
        }

        public void UpdateLanguage(string language)
        {
            _settings.Language = language;
            SaveSettings();
        }

        public void UpdateTheme(string theme)
        {
            _settings.Theme = theme;
            SaveSettings();
        }

        public void UpdateOutputDirectory(string directory)
        {
            _settings.OutputDirectory = directory;
            SaveSettings();
        }

        public void UpdateDefaultOutputFormat(string format)
        {
            _settings.DefaultOutputFormat = format;
            SaveSettings();
        }

        public void UpdateShowCountdown(bool show)
        {
            _settings.ShowCountdown = show;
            SaveSettings();
        }

        public void UpdateAutoCloseAfterCutting(bool autoClose)
        {
            _settings.AutoCloseAfterCutting = autoClose;
            SaveSettings();
        }

        public void UpdatePlayNotificationSound(bool playSound)
        {
            _settings.PlayNotificationSound = playSound;
            SaveSettings();
        }

        public void UpdateWindowSettings(double width, double height, double left, double top, bool isMaximized)
        {
            _settings.WindowWidth = width;
            _settings.WindowHeight = height;
            _settings.WindowLeft = left;
            _settings.WindowTop = top;
            _settings.IsMaximized = isMaximized;
            SaveSettings();
        }
    }
}
