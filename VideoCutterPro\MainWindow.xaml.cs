﻿using System.ComponentModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;
using VideoCutterPro.Models;
using VideoCutterPro.Services;
using VideoCutterPro.Views;

namespace VideoCutterPro;

public partial class MainWindow : Window, INotifyPropertyChanged
{
    private readonly VideoService _videoService;
    private readonly DispatcherTimer _positionTimer;
    private bool _isUserDragging = false;
    private bool _isPlaying = false;
    private CancellationTokenSource? _cuttingCancellationTokenSource;

    public VideoInfo VideoInfo { get; set; }
    public LocalizationService LocalizationService { get; set; }
    public SettingsService SettingsService { get; set; }

    public bool CanStartCutting => VideoInfo.IsLoaded &&
                                   VideoInfo.StartTime < VideoInfo.EndTime &&
                                   VideoInfo.CutDuration.TotalSeconds > 0;

    public MainWindow()
    {
        InitializeComponent();

        // Initialize services
        _videoService = new VideoService();
        LocalizationService = LocalizationService.Instance;
        SettingsService = SettingsService.Instance;
        VideoInfo = new VideoInfo();

        // Setup data context
        DataContext = this;

        // Initialize timer for position updates
        _positionTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100)
        };
        _positionTimer.Tick += PositionTimer_Tick;

        // Setup video service events
        _videoService.ProgressChanged += VideoService_ProgressChanged;
        _videoService.TimeRemainingChanged += VideoService_TimeRemainingChanged;
        _videoService.StatusChanged += VideoService_StatusChanged;

        // Setup property change notifications
        VideoInfo.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(VideoInfo.StartTime) ||
                e.PropertyName == nameof(VideoInfo.EndTime) ||
                e.PropertyName == nameof(VideoInfo.IsLoaded))
            {
                OnPropertyChanged(nameof(CanStartCutting));
            }
        };

        // Load settings
        LoadSettings();

        // Setup drag and drop
        AllowDrop = true;
        Drop += MainWindow_Drop;
        DragEnter += MainWindow_DragEnter;
        DragOver += MainWindow_DragOver;

        // Setup window events
        Closing += MainWindow_Closing;
        StateChanged += MainWindow_StateChanged;
        LocationChanged += MainWindow_LocationChanged;
        SizeChanged += MainWindow_SizeChanged;

        // Apply theme and language
        ApplyTheme();
        ApplyLanguage();
    }

    private void LoadSettings()
    {
        var settings = SettingsService.Settings;

        // Apply window settings
        if (settings.WindowWidth > 0 && settings.WindowHeight > 0)
        {
            Width = settings.WindowWidth;
            Height = settings.WindowHeight;
            Left = settings.WindowLeft;
            Top = settings.WindowTop;
        }

        if (settings.IsMaximized)
        {
            WindowState = WindowState.Maximized;
        }

        // Apply language
        LocalizationService.CurrentLanguage = settings.Language;
    }

    private void ApplyTheme()
    {
        ThemeService.Instance.ApplyTheme(SettingsService.Settings.Theme);
    }

    private void ApplyLanguage()
    {
        // Language is applied through binding
        OnPropertyChanged(nameof(LocalizationService));
    }

    // Window Events
    private void MainWindow_Closing(object? sender, CancelEventArgs e)
    {
        // Save window settings
        var settings = SettingsService.Settings;
        if (WindowState == WindowState.Normal)
        {
            settings.WindowWidth = Width;
            settings.WindowHeight = Height;
            settings.WindowLeft = Left;
            settings.WindowTop = Top;
        }
        settings.IsMaximized = WindowState == WindowState.Maximized;
        SettingsService.SaveSettings();

        // Stop video and cleanup
        StopVideo();
        _cuttingCancellationTokenSource?.Cancel();
    }

    private void MainWindow_StateChanged(object? sender, EventArgs e)
    {
        // Update maximize button icon
        if (MaximizeButton != null)
        {
            var icon = WindowState == WindowState.Maximized ?
                MaterialDesignThemes.Wpf.PackIconKind.WindowRestore :
                MaterialDesignThemes.Wpf.PackIconKind.WindowMaximize;
            ((MaterialDesignThemes.Wpf.PackIcon)MaximizeButton.Content).Kind = icon;
        }
    }

    private void MainWindow_LocationChanged(object? sender, EventArgs e)
    {
        // Auto-save location if not maximized
        if (WindowState == WindowState.Normal)
        {
            SettingsService.Settings.WindowLeft = Left;
            SettingsService.Settings.WindowTop = Top;
        }
    }

    private void MainWindow_SizeChanged(object? sender, SizeChangedEventArgs e)
    {
        // Auto-save size if not maximized
        if (WindowState == WindowState.Normal)
        {
            SettingsService.Settings.WindowWidth = Width;
            SettingsService.Settings.WindowHeight = Height;
        }
    }

    // Title Bar Events
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        if (settingsWindow.ShowDialog() == true)
        {
            // Refresh UI after settings change
            ApplyTheme();
            ApplyLanguage();
        }
    }

    // Drag and Drop Events
    private void MainWindow_DragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            e.Effects = DragDropEffects.Copy;
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
    }

    private void MainWindow_DragOver(object sender, DragEventArgs e)
    {
        MainWindow_DragEnter(sender, e);
    }

    private async void MainWindow_Drop(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files.Length > 0 && VideoService.IsVideoFile(files[0]))
            {
                await LoadVideoAsync(files[0]);
            }
            else
            {
                ShowMessage(LocalizationService.GetString("InvalidVideoFile"), LocalizationService.GetString("Error"));
            }
        }
    }

    // Video Control Events
    private void SelectVideoButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = LocalizationService.GetString("SelectVideoFile"),
            Filter = $"{LocalizationService.GetString("VideoFiles")} (*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm)|*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm|{LocalizationService.GetString("AllFiles")} (*.*)|*.*"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            _ = LoadVideoAsync(openFileDialog.FileName);
        }
    }

    private void PlayButton_Click(object sender, RoutedEventArgs e)
    {
        if (VideoInfo.IsLoaded)
        {
            PlayVideo();
        }
    }

    private void PauseButton_Click(object sender, RoutedEventArgs e)
    {
        PauseVideo();
    }

    private void StopButton_Click(object sender, RoutedEventArgs e)
    {
        StopVideo();
    }

    private void SetStartTimeButton_Click(object sender, RoutedEventArgs e)
    {
        if (VideoInfo.IsLoaded)
        {
            VideoInfo.StartTime = VideoInfo.CurrentPosition;
        }
    }

    private void SetEndTimeButton_Click(object sender, RoutedEventArgs e)
    {
        if (VideoInfo.IsLoaded)
        {
            VideoInfo.EndTime = VideoInfo.CurrentPosition;
        }
    }

    private async void StartCuttingButton_Click(object sender, RoutedEventArgs e)
    {
        if (!CanStartCutting)
        {
            ShowMessage(LocalizationService.GetString("NoVideoSelected"), LocalizationService.GetString("Warning"));
            return;
        }

        if (VideoInfo.StartTime >= VideoInfo.EndTime)
        {
            ShowMessage(LocalizationService.GetString("InvalidTimeRange"), LocalizationService.GetString("Warning"));
            return;
        }

        await StartCuttingAsync();
    }

    // Video Player Events
    private void VideoPlayer_MediaOpened(object sender, RoutedEventArgs e)
    {
        if (VideoPlayer.NaturalDuration.HasTimeSpan)
        {
            VideoInfo.Duration = VideoPlayer.NaturalDuration.TimeSpan;
            VideoInfo.EndTime = VideoInfo.Duration;
            TimelineSlider.Maximum = VideoInfo.Duration.TotalSeconds;
        }
    }

    private void VideoPlayer_MediaEnded(object sender, RoutedEventArgs e)
    {
        StopVideo();
    }

    private void VideoPlayer_MediaFailed(object sender, ExceptionRoutedEventArgs e)
    {
        ShowMessage($"{LocalizationService.GetString("Error")}: {e.ErrorException?.Message}", LocalizationService.GetString("Error"));
    }

    // Timeline Events
    private void TimelineSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (!_isUserDragging && VideoInfo.IsLoaded)
        {
            var position = TimeSpan.FromSeconds(e.NewValue);
            VideoPlayer.Position = position;
            VideoInfo.CurrentPosition = position;
        }
    }

    private void TimelineSlider_DragStarted(object sender, DragStartedEventArgs e)
    {
        _isUserDragging = true;
    }

    private void TimelineSlider_DragCompleted(object sender, DragCompletedEventArgs e)
    {
        _isUserDragging = false;
        if (VideoInfo.IsLoaded)
        {
            var position = TimeSpan.FromSeconds(TimelineSlider.Value);
            VideoPlayer.Position = position;
            VideoInfo.CurrentPosition = position;
        }
    }

    // Timer Events
    private void PositionTimer_Tick(object? sender, EventArgs e)
    {
        if (VideoInfo.IsLoaded && !_isUserDragging && _isPlaying)
        {
            VideoInfo.CurrentPosition = VideoPlayer.Position;
            TimelineSlider.Value = VideoInfo.CurrentPosition.TotalSeconds;
        }
    }

    // Video Service Events
    private void VideoService_ProgressChanged(object? sender, double progress)
    {
        Dispatcher.Invoke(() =>
        {
            // Update progress in cutting dialog if open
            StatusTextBlock.Text = $"{LocalizationService.GetString("Progress")}: {progress:F1}%";
        });
    }

    private void VideoService_TimeRemainingChanged(object? sender, TimeSpan timeRemaining)
    {
        Dispatcher.Invoke(() =>
        {
            StatusTextBlock.Text = $"{LocalizationService.GetString("TimeRemaining")}: {timeRemaining:mm\\:ss}";
        });
    }

    private void VideoService_StatusChanged(object? sender, string status)
    {
        Dispatcher.Invoke(() =>
        {
            StatusTextBlock.Text = status;
        });
    }

    // Video Methods
    private async Task LoadVideoAsync(string filePath)
    {
        try
        {
            StatusTextBlock.Text = "Loading video...";

            // Stop current video
            StopVideo();

            // Load video info
            var videoInfo = await _videoService.LoadVideoInfoAsync(filePath);
            if (videoInfo != null)
            {
                VideoInfo.FilePath = videoInfo.FilePath;
                VideoInfo.Duration = videoInfo.Duration;
                VideoInfo.EndTime = videoInfo.Duration;
                VideoInfo.StartTime = TimeSpan.Zero;
                VideoInfo.CurrentPosition = TimeSpan.Zero;
                VideoInfo.FileSize = videoInfo.FileSize;
                VideoInfo.Format = videoInfo.Format;
                VideoInfo.Width = videoInfo.Width;
                VideoInfo.Height = videoInfo.Height;
                VideoInfo.FrameRate = videoInfo.FrameRate;
                VideoInfo.IsLoaded = true;

                // Load video in player
                VideoPlayer.Source = new Uri(filePath);

                StatusTextBlock.Text = "Video loaded successfully";
            }
            else
            {
                ShowMessage(LocalizationService.GetString("InvalidVideoFile"), LocalizationService.GetString("Error"));
                StatusTextBlock.Text = "Failed to load video";
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"{LocalizationService.GetString("Error")}: {ex.Message}", LocalizationService.GetString("Error"));
            StatusTextBlock.Text = "Error loading video";
        }
    }

    private void PlayVideo()
    {
        if (VideoInfo.IsLoaded)
        {
            VideoPlayer.Play();
            _isPlaying = true;
            _positionTimer.Start();
            StatusTextBlock.Text = "Playing";
        }
    }

    private void PauseVideo()
    {
        if (VideoInfo.IsLoaded)
        {
            VideoPlayer.Pause();
            _isPlaying = false;
            _positionTimer.Stop();
            StatusTextBlock.Text = "Paused";
        }
    }

    private void StopVideo()
    {
        if (VideoInfo.IsLoaded)
        {
            VideoPlayer.Stop();
            _isPlaying = false;
            _positionTimer.Stop();
            VideoInfo.CurrentPosition = TimeSpan.Zero;
            TimelineSlider.Value = 0;
            StatusTextBlock.Text = "Stopped";
        }
    }

    private async Task StartCuttingAsync()
    {
        try
        {
            // Stop video playback
            StopVideo();

            // Create output file name
            var outputFormat = SettingsService.Settings.DefaultOutputFormat;
            var outputFileName = VideoService.GetOutputFileName(VideoInfo.FilePath, outputFormat, VideoInfo.StartTime, VideoInfo.EndTime);
            var outputPath = Path.Combine(SettingsService.Settings.OutputDirectory, outputFileName);

            // Show cutting dialog
            var cuttingDialog = new CuttingProgressWindow(VideoInfo, outputPath);
            cuttingDialog.Owner = this;
            cuttingDialog.Show();

            // Start cutting process
            _cuttingCancellationTokenSource = new CancellationTokenSource();

            // Connect progress events to dialog
            _videoService.ProgressChanged += (s, progress) => cuttingDialog.UpdateProgress(progress);
            _videoService.TimeRemainingChanged += (s, time) => cuttingDialog.UpdateTimeRemaining(time);
            _videoService.StatusChanged += (s, status) => cuttingDialog.UpdateStatus(status);

            var result = await _videoService.CutVideoAsync(VideoInfo, outputPath, outputFormat, _cuttingCancellationTokenSource.Token);

            // Update dialog with result
            cuttingDialog.SetCompleted(result);

            if (result)
            {
                if (SettingsService.Settings.PlayNotificationSound)
                {
                    AudioService.Instance.PlaySuccessSound();
                }

                ShowMessage($"{LocalizationService.GetString("CuttingCompleted")}\n{LocalizationService.GetString("OutputSaved")} {outputPath}",
                           LocalizationService.GetString("Success"));

                if (SettingsService.Settings.AutoCloseAfterCutting)
                {
                    Close();
                }
            }
            else
            {
                if (SettingsService.Settings.PlayNotificationSound)
                {
                    AudioService.Instance.PlayErrorSound();
                }

                ShowMessage(LocalizationService.GetString("CuttingFailed"), LocalizationService.GetString("Error"));
            }
        }
        catch (OperationCanceledException)
        {
            StatusTextBlock.Text = "Cutting cancelled";
        }
        catch (Exception ex)
        {
            if (SettingsService.Settings.PlayNotificationSound)
            {
                AudioService.Instance.PlayErrorSound();
            }

            ShowMessage($"{LocalizationService.GetString("Error")}: {ex.Message}", LocalizationService.GetString("Error"));
        }
        finally
        {
            _cuttingCancellationTokenSource?.Dispose();
            _cuttingCancellationTokenSource = null;
        }
    }

    // Helper Methods
    private void ShowMessage(string message, string title)
    {
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // INotifyPropertyChanged Implementation
    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}