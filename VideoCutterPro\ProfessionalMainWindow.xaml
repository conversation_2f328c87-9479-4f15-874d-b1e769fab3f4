<Window x:Class="VideoCutterPro.ProfessionalMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Video Cutter Pro"
        Height="750" Width="1000"
        MinHeight="650" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Background="#F0F0F0"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResizeWithGrip">
    
    <Window.Resources>
        <!-- Custom Styles -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#4A4A4A"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A5A5A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A3A3A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StartButton" TargetType="Button">
            <Setter Property="Background" Value="#7CB342"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#8BC34A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#689F38"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="8" BorderBrush="#CCCCCC" BorderThickness="1">
        <Grid>
            <Grid.RowDefinitions>
                <!-- Title Bar -->
                <RowDefinition Height="30"/>
                <!-- Video Area -->
                <RowDefinition Height="*"/>
                <!-- Controls Area -->
                <RowDefinition Height="120"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar -->
            <Border Name="TitleBar" Grid.Row="0" Background="#E0E0E0" CornerRadius="8,8,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <TextBlock Text="🎬" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Text="Video Cutter Pro - قاطع الفيديو الاحترافي" FontSize="14" FontWeight="Bold" Foreground="#333" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Name="MinimizeBtn" Content="🗕" Width="45" Height="30"
                                Background="Transparent" BorderThickness="0" FontSize="14"
                                Foreground="#666" Click="MinimizeButton_Click"
                                ToolTip="تصغير | Minimize">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E0E0E0"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                        <Button Name="MaximizeBtn" Content="🗖" Width="45" Height="30"
                                Background="Transparent" BorderThickness="0" FontSize="14"
                                Foreground="#666" Click="MaximizeButton_Click"
                                ToolTip="تكبير | Maximize">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E0E0E0"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                        <Button Name="CloseBtn" Content="🗙" Width="45" Height="30"
                                Background="Transparent" BorderThickness="0" FontSize="14"
                                Foreground="#666" Click="CloseButton_Click"
                                ToolTip="إغلاق | Close">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#FF4444"/>
                                            <Setter Property="Foreground" Value="White"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Video Display Area -->
            <Border Grid.Row="1" Background="Black" Margin="10,5,10,5" CornerRadius="5">
                <Grid>
                    <!-- Video Player -->
                    <MediaElement Name="VideoPlayer" 
                                  LoadedBehavior="Manual" 
                                  UnloadedBehavior="Manual"
                                  Stretch="Uniform"/>
                    
                    <!-- Logo Overlay -->
                    <StackPanel VerticalAlignment="Top" HorizontalAlignment="Right" Margin="20">
                        <Border Background="#AA000000" CornerRadius="5" Padding="10">
                            <TextBlock Text="Video Cutter Pro" Foreground="#FFD700" FontWeight="Bold" FontSize="14"/>
                        </Border>
                    </StackPanel>
                    
                    <!-- Drag Drop Overlay -->
                    <Border Name="DragDropOverlay" Background="#AA000000"
                            Visibility="Visible">
                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                            <TextBlock Text="🎬" FontSize="80" HorizontalAlignment="Center" Foreground="#7CB342" Margin="0,0,0,20"/>
                            <TextBlock Text="اسحب ملف فيديو هنا أو انقر لاختيار الملف"
                                       FontSize="18" Foreground="White" FontWeight="Bold"
                                       HorizontalAlignment="Center" Margin="0,0,0,10" TextAlignment="Center"/>
                            <TextBlock Text="Drag video file here or click to select file"
                                       FontSize="14" Foreground="#CCCCCC"
                                       HorizontalAlignment="Center" Margin="0,0,0,25" TextAlignment="Center"/>
                            <Button Content="📁 اختيار ملف فيديو | Select Video File"
                                    Background="#7CB342" Foreground="White"
                                    FontSize="16" FontWeight="Bold"
                                    Padding="25,12" Margin="0,10,0,0"
                                    BorderThickness="0" Cursor="Hand"
                                    Click="BrowseButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#7CB342"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#8BC34A"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter Property="Background" Value="#689F38"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- Controls Area -->
            <Border Grid.Row="2" Background="#F5F5F5" Margin="10,5,10,10" CornerRadius="5"
                    BorderBrush="#E0E0E0" BorderThickness="1">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <!-- Playback Controls -->
                        <RowDefinition Height="40"/>
                        <!-- Timeline -->
                        <RowDefinition Height="25"/>
                        <!-- Cut Controls -->
                        <RowDefinition Height="40"/>
                    </Grid.RowDefinitions>

                    <!-- Playback Controls Row -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>

                        <!-- Time Display -->
                        <TextBlock Grid.Column="0" Name="TimeDisplay"
                                   Text="0:00:00.00 / 0:11:01.81"
                                   VerticalAlignment="Center"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="#333"/>

                        <!-- Playback Buttons -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal"
                                    HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Button Content="⏮" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                            <Button Content="⏪" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                            <Button Name="PlayPauseButton" Content="▶" Style="{StaticResource ModernButton}"
                                    Width="35" Height="30" Click="PlayPauseButton_Click"/>
                            <Button Content="⏹" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                            <Button Content="⏩" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                            <Button Content="⏭" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                            <Button Content="⏸" Style="{StaticResource ModernButton}" Width="30" Height="30"/>
                        </StackPanel>

                        <!-- Volume -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal"
                                    VerticalAlignment="Center" HorizontalAlignment="Right">
                            <TextBlock Text="🔊" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <Rectangle Width="50" Height="4" Fill="#7CB342"/>
                        </StackPanel>
                    </Grid>

                    <!-- Timeline Row -->
                    <Grid Grid.Row="1" Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                        </Grid.ColumnDefinitions>

                        <!-- Play Button -->
                        <Button Grid.Column="0" Content="▶" Width="16" Height="16"
                                Style="{StaticResource ModernButton}" FontSize="8"/>

                        <!-- Timeline Slider -->
                        <Grid Grid.Column="1">
                            <!-- Background Track -->
                            <Rectangle Height="6" Fill="#E0E0E0" VerticalAlignment="Center"/>
                            <!-- Progress Track -->
                            <Rectangle Name="ProgressTrack" Height="6" Fill="#7CB342"
                                       HorizontalAlignment="Left" Width="0" VerticalAlignment="Center"/>
                            <!-- Slider -->
                            <Slider Name="TimelineSlider"
                                    Minimum="0" Maximum="100" Value="0"
                                    Background="Transparent"
                                    Foreground="#7CB342"
                                    Height="20"
                                    ValueChanged="TimelineSlider_ValueChanged"/>
                        </Grid>

                        <!-- End Marker -->
                        <Button Grid.Column="2" Content="◀" Width="16" Height="16"
                                Style="{StaticResource ModernButton}" FontSize="8"/>
                    </Grid>

                    <!-- Cut Controls Row -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                        </Grid.ColumnDefinitions>

                        <!-- Folder Icon -->
                        <Button Grid.Column="0" Content="📁"
                                Background="#4A4A4A" Foreground="White"
                                Width="55" Height="40" FontSize="18"
                                BorderThickness="0" Cursor="Hand"
                                Click="BrowseButton_Click"
                                ToolTip="اختيار ملف فيديو | Select video file">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="#4A4A4A"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#5A5A5A"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#3A3A3A"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>

                        <!-- Cut Start -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                            <Button Content="[" Style="{StaticResource ModernButton}" Width="25" Height="25"
                                    Click="SetStartButton_Click"/>
                            <TextBox Name="StartTimeBox" Text="0:00:00.00" Width="80" Height="25"
                                     VerticalContentAlignment="Center" FontFamily="Consolas" FontSize="11"
                                     Margin="5,0"/>
                        </StackPanel>

                        <!-- Arrow -->
                        <TextBlock Grid.Column="2" Text="→" VerticalAlignment="Center"
                                   FontSize="16" Margin="5,0"/>

                        <!-- Cut End -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                            <TextBox Name="EndTimeBox" Text="0:11:01.81" Width="80" Height="25"
                                     VerticalContentAlignment="Center" FontFamily="Consolas" FontSize="11"
                                     Margin="0,0,5,0"/>
                            <Button Content="]" Style="{StaticResource ModernButton}" Width="25" Height="25"
                                    Click="SetEndButton_Click"/>
                        </StackPanel>

                        <!-- Duration Label -->
                        <TextBlock Grid.Column="4" Name="DurationLabel" Text="Duration 0:11:01.81"
                                   VerticalAlignment="Center" FontSize="11" Foreground="#666" Margin="15,0"/>

                        <!-- Start Button -->
                        <Button Grid.Column="6" Content="🚀 Start Cutting"
                                Background="#7CB342" Foreground="White"
                                FontSize="16" FontWeight="Bold"
                                Height="40" MinWidth="150" Padding="20,8"
                                BorderThickness="0" Cursor="Hand"
                                Click="StartButton_Click"
                                ToolTip="بدء عملية القص | Start cutting process">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="#7CB342"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="8" Padding="{TemplateBinding Padding}"
                                                        BorderThickness="{TemplateBinding BorderThickness}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#8BC34A"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#689F38"/>
                                                    </Trigger>
                                                    <Trigger Property="IsEnabled" Value="False">
                                                        <Setter Property="Background" Value="#CCCCCC"/>
                                                        <Setter Property="Foreground" Value="#666666"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
