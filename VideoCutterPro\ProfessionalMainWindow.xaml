<Window x:Class="VideoCutterPro.ProfessionalMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Video Cutter Pro"
        Height="625" Width="908"
        MinHeight="600" MinWidth="850"
        WindowStartupLocation="CenterScreen"
        Background="#F0F0F0"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResizeWithGrip">
    
    <Window.Resources>
        <!-- Bandicut-Style Custom Styles -->

        <!-- Black Control Bar Button Style -->
        <Style x:Key="BlackControlButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="2"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#333333"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#555555"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Green Start Button Style (Bandicut Style) -->
        <Style x:Key="BandicutStartButton" TargetType="Button">
            <Setter Property="Background" Value="#7CB342"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#8BC34A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#689F38"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Gray Button Style -->
        <Style x:Key="GrayButton" TargetType="Button">
            <Setter Property="Background" Value="#E0E0E0"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F0F0"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D0D0D0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="8" BorderBrush="#CCCCCC" BorderThickness="1">
        <Grid>
            <Grid.RowDefinitions>
                <!-- Title Bar -->
                <RowDefinition Height="35"/>
                <!-- Video Area -->
                <RowDefinition Height="*"/>
                <!-- Controls Area -->
                <RowDefinition Height="240"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar (Bandicut Style) -->
            <Border Name="TitleBar" Grid.Row="0" Background="#F0F0F0" CornerRadius="8,8,0,0"
                    BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- App Icon -->
                    <TextBlock Grid.Column="0" Text="🎬" FontSize="16" VerticalAlignment="Center"
                               HorizontalAlignment="Center" Foreground="#7CB342"/>

                    <!-- Title -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="5,0">
                        <TextBlock Name="TitleText" Text="Video Cutter Pro" FontSize="13" FontWeight="Normal"
                                   Foreground="#333" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- File Info -->
                    <TextBlock Grid.Column="2" Name="FileInfoText" Text="" FontSize="12"
                               VerticalAlignment="Center" Foreground="#666" Margin="0,0,15,0"/>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                        <Button Name="MinimizeBtn" Content="🗕" Width="45" Height="35"
                                Background="Transparent" BorderThickness="0" FontSize="12"
                                Foreground="#666" Click="MinimizeButton_Click"
                                ToolTip="تصغير | Minimize">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E0E0E0"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                        <Button Name="MaximizeBtn" Content="🗖" Width="45" Height="35"
                                Background="Transparent" BorderThickness="0" FontSize="12"
                                Foreground="#666" Click="MaximizeButton_Click"
                                ToolTip="تكبير | Maximize">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E0E0E0"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                        <Button Name="CloseBtn" Content="🗙" Width="45" Height="35"
                                Background="Transparent" BorderThickness="0" FontSize="12"
                                Foreground="#666" Click="CloseButton_Click"
                                ToolTip="إغلاق | Close">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#CC0000"/>
                                            <Setter Property="Foreground" Value="White"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Video Display Area (Bandicut Style) -->
            <Border Grid.Row="1" Background="#F5F5F5" Margin="8,5,8,0" CornerRadius="0">
                <Grid>
                    <!-- Main Video Container -->
                    <Border Background="Black" Margin="0" CornerRadius="0">
                        <Grid>
                            <!-- Video Player -->
                            <MediaElement Name="VideoPlayer"
                                          LoadedBehavior="Manual"
                                          UnloadedBehavior="Manual"
                                          Stretch="Uniform"/>

                            <!-- Video Info Overlay (Top Left) -->
                            <StackPanel VerticalAlignment="Top" HorizontalAlignment="Left" Margin="15,15">
                                <TextBlock Name="VideoInfoText" Text="Trump.mp4 (1280x720)"
                                           Foreground="White" FontWeight="Bold" FontSize="12"
                                           Background="#AA000000" Padding="8,4" Opacity="0.8"/>
                            </StackPanel>

                            <!-- Drag Drop Overlay -->
                            <Border Name="DragDropOverlay" Background="#AA000000"
                                    Visibility="Visible">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <TextBlock Text="🎬" FontSize="80" HorizontalAlignment="Center" Foreground="#7CB342" Margin="0,0,0,20"/>
                                    <TextBlock Text="اسحب ملف فيديو هنا أو انقر لاختيار الملف"
                                               FontSize="18" Foreground="White" FontWeight="Bold"
                                               HorizontalAlignment="Center" Margin="0,0,0,10" TextAlignment="Center"/>
                                    <TextBlock Text="Drag video file here or click to select file"
                                               FontSize="14" Foreground="#CCCCCC"
                                               HorizontalAlignment="Center" Margin="0,0,0,25" TextAlignment="Center"/>
                                    <Button Content="📁 اختيار ملف فيديو | Select Video File"
                                            Style="{StaticResource BandicutStartButton}"
                                            Padding="25,12" Margin="0,10,0,0"
                                            Click="BrowseButton_Click"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Black Control Bar (Bandicut Style) -->
                    <Border VerticalAlignment="Bottom" Background="#1A1A1A" Height="50" Margin="0">
                        <Grid Margin="15,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>

                            <!-- Time Display -->
                            <TextBlock Grid.Column="0" Name="VideoTimeDisplay"
                                       Text="0:00:00.00 / 0:00:26.05"
                                       VerticalAlignment="Center"
                                       FontFamily="Consolas"
                                       FontSize="12"
                                       Foreground="White"/>

                            <!-- Control Buttons -->
                            <StackPanel Grid.Column="1" Orientation="Horizontal"
                                        HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Button Content="⏮" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                                <Button Content="⏪" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                                <Button Name="MainPlayPauseButton" Content="▶" Style="{StaticResource BlackControlButton}"
                                        Width="40" Height="32" Click="PlayPauseButton_Click" FontSize="16"/>
                                <Button Content="⏹" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                                <Button Content="⏸" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                                <Button Content="⏩" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                                <Button Content="⏭" Style="{StaticResource BlackControlButton}" Width="32" Height="32"/>
                            </StackPanel>

                            <!-- Volume and Progress -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal"
                                        VerticalAlignment="Center" HorizontalAlignment="Right">
                                <TextBlock Text="🔊" VerticalAlignment="Center" Margin="0,0,8,0" Foreground="White"/>
                                <Rectangle Width="60" Height="4" Fill="#7CB342" VerticalAlignment="Center"/>
                                <Rectangle Width="20" Height="4" Fill="#333" VerticalAlignment="Center" Margin="2,0,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </Border>

            <!-- Controls Area (Bandicut Style) -->
            <Border Grid.Row="2" Background="#F8F8F8" Margin="8,0,8,8" CornerRadius="0"
                    BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                <Grid Margin="12">
                    <Grid.RowDefinitions>
                        <!-- Main Timeline -->
                        <RowDefinition Height="60"/>
                        <!-- Cut Controls -->
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Main Timeline Row (Bandicut Style) -->
                    <Grid Grid.Row="0" Margin="0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="25"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="25"/>
                        </Grid.ColumnDefinitions>

                        <!-- Play Button -->
                        <Button Grid.Column="0" Content="▶" Width="20" Height="20"
                                Style="{StaticResource GrayButton}" FontSize="10"
                                VerticalAlignment="Center"/>

                        <!-- Main Timeline -->
                        <Grid Grid.Column="1" Margin="8,0">
                            <!-- Background Track -->
                            <Rectangle Height="8" Fill="#E0E0E0" VerticalAlignment="Center" RadiusX="4" RadiusY="4"/>
                            <!-- Progress Track (Green like Bandicut) -->
                            <Rectangle Name="MainProgressTrack" Height="8" Fill="#7CB342"
                                       HorizontalAlignment="Left" Width="0" VerticalAlignment="Center" RadiusX="4" RadiusY="4"/>
                            <!-- Timeline Slider -->
                            <Slider Name="MainTimelineSlider"
                                    Minimum="0" Maximum="100" Value="0"
                                    Background="Transparent"
                                    Height="25"
                                    ValueChanged="TimelineSlider_ValueChanged">
                                <Slider.Style>
                                    <Style TargetType="Slider">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Slider">
                                                    <Grid>
                                                        <Track Name="PART_Track">
                                                            <Track.Thumb>
                                                                <Thumb>
                                                                    <Thumb.Style>
                                                                        <Style TargetType="Thumb">
                                                                            <Setter Property="Template">
                                                                                <Setter.Value>
                                                                                    <ControlTemplate TargetType="Thumb">
                                                                                        <Ellipse Fill="#7CB342" Width="12" Height="12" Stroke="White" StrokeThickness="2"/>
                                                                                    </ControlTemplate>
                                                                                </Setter.Value>
                                                                            </Setter>
                                                                        </Style>
                                                                    </Thumb.Style>
                                                                </Thumb>
                                                            </Track.Thumb>
                                                        </Track>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Slider.Style>
                            </Slider>
                        </Grid>

                        <!-- End Marker -->
                        <Button Grid.Column="2" Content="◀" Width="20" Height="20"
                                Style="{StaticResource GrayButton}" FontSize="10"
                                VerticalAlignment="Center"/>
                    </Grid>

                    <!-- Cut Controls Row (Bandicut Style) -->
                    <Grid Grid.Row="1" Margin="0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="90"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="180"/>
                        </Grid.ColumnDefinitions>

                        <!-- Folder Icon (Bandicut Style) -->
                        <Button Grid.Column="0" Content="📁"
                                Style="{StaticResource GrayButton}"
                                Width="75" Height="60" FontSize="20"
                                Click="BrowseButton_Click"
                                ToolTip="اختيار ملف فيديو | Select video file"
                                HorizontalAlignment="Center" VerticalAlignment="Center"
                                Margin="5,0"/>

                        <!-- Cut Start (Bandicut Style) -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                            <Button Content="[" Style="{StaticResource GrayButton}" Width="35" Height="45"
                                    Click="SetStartButton_Click" FontSize="16" FontWeight="Bold"/>
                            <TextBox Name="StartTimeBox" Text="0:00:00.00" Width="90" Height="45"
                                     VerticalContentAlignment="Center" FontFamily="Consolas" FontSize="12"
                                     Margin="8,0" BorderBrush="#CCCCCC" BorderThickness="1"
                                     Background="White" Foreground="#333"/>
                        </StackPanel>

                        <!-- Arrow -->
                        <TextBlock Grid.Column="2" Text="~" VerticalAlignment="Center"
                                   FontSize="18" Margin="8,0" Foreground="#666" FontWeight="Bold"/>

                        <!-- Cut End (Bandicut Style) -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                            <TextBox Name="EndTimeBox" Text="0:00:26.05" Width="90" Height="45"
                                     VerticalContentAlignment="Center" FontFamily="Consolas" FontSize="12"
                                     Margin="0,0,8,0" BorderBrush="#CCCCCC" BorderThickness="1"
                                     Background="White" Foreground="#333"/>
                            <Button Content="]" Style="{StaticResource GrayButton}" Width="35" Height="45"
                                    Click="SetEndButton_Click" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <!-- Duration Label -->
                        <TextBlock Grid.Column="4" Name="DurationLabel" Text="Duration 0:00:26.05"
                                   VerticalAlignment="Center" FontSize="13" Foreground="#666" Margin="20,0"
                                   FontWeight="Normal"/>

                        <!-- Start Button (Bandicut Style) -->
                        <Button Grid.Column="6" Content="Start"
                                Style="{StaticResource BandicutStartButton}"
                                Height="60" Width="140"
                                Click="StartButton_Click"
                                ToolTip="بدء عملية القص | Start cutting process"
                                HorizontalAlignment="Center" VerticalAlignment="Center"
                                Margin="10,0"/>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
