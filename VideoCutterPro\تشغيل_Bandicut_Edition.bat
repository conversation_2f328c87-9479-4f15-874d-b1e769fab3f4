@echo off
chcp 65001 > nul
title Video Cutter Pro - Bandicut Edition

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🎬 Video Cutter Pro - Bandicut Edition 🎬                ║
echo ║                                                              ║
echo ║    تطبيق قص الفيديو الاحترافي - إصدار Bandicut              ║
echo ║    Professional Video Cutting - Bandicut Style              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 بدء تشغيل التطبيق... | Starting Application...
echo.

REM Check if .NET is installed
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: .NET غير مثبت | Error: .NET not installed
    echo.
    echo يرجى تثبيت .NET 9.0 Desktop Runtime من:
    echo Please install .NET 9.0 Desktop Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/9.0
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على .NET | .NET Found
echo.

REM Check if project exists
if not exist "VideoCutterPro.csproj" (
    echo ❌ خطأ: ملف المشروع غير موجود | Error: Project file not found
    echo تأكد من وجودك في مجلد المشروع | Make sure you're in the project folder
    echo.
    pause
    exit /b 1
)

echo ✅ ملف المشروع موجود | Project file found
echo.

echo 🔨 بناء المشروع... | Building project...
dotnet build --configuration Release --verbosity quiet

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع | Build failed
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح | Build successful
echo.

echo 🎬 تشغيل Video Cutter Pro - Bandicut Edition...
echo.
echo ميزات هذا الإصدار | Features of this version:
echo ▶ تصميم مطابق لـ Bandicut | Bandicut-matching design
echo ▶ نظام وقت متقدم | Advanced time system  
echo ▶ واجهة احترافية | Professional interface
echo ▶ دعم ثنائي اللغة | Bilingual support
echo.

REM Run the application in professional mode
dotnet run --configuration Release -- professional

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق | Error occurred while running the application
    echo.
    echo استكشاف الأخطاء | Troubleshooting:
    echo 1. تأكد من تثبيت .NET 9.0 Desktop Runtime
    echo 2. تأكد من وجود جميع ملفات المشروع
    echo 3. راجع ملف استكشاف_الأخطاء.md
    echo.
    echo 1. Make sure .NET 9.0 Desktop Runtime is installed
    echo 2. Make sure all project files are present  
    echo 3. Check استكشاف_الأخطاء.md file
    echo.
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح | Application closed successfully
    echo.
)

echo.
echo شكراً لاستخدام Video Cutter Pro! | Thank you for using Video Cutter Pro!
echo.
pause
