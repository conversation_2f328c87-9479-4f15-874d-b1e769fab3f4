using System.Media;
using System.Windows;

namespace VideoCutterPro.Services
{
    public class AudioService
    {
        private static AudioService? _instance;
        public static AudioService Instance => _instance ??= new AudioService();

        private AudioService() { }

        public void PlayNotificationSound()
        {
            try
            {
                // Play system notification sound
                SystemSounds.Asterisk.Play();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing notification sound: {ex.Message}");
            }
        }

        public void PlaySuccessSound()
        {
            try
            {
                // Play system success sound
                SystemSounds.Exclamation.Play();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing success sound: {ex.Message}");
            }
        }

        public void PlayErrorSound()
        {
            try
            {
                // Play system error sound
                SystemSounds.Hand.Play();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing error sound: {ex.Message}");
            }
        }

        public void PlayWarningSound()
        {
            try
            {
                // Play system warning sound
                SystemSounds.Beep.Play();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing warning sound: {ex.Message}");
            }
        }
    }
}
