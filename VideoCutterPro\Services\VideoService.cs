using FFMpegCore;
using FFMpegCore.Enums;
using VideoCutterPro.Models;
using System.IO;

namespace VideoCutterPro.Services
{
    public class VideoService
    {
        public event EventHandler<double>? ProgressChanged;
        public event EventHandler<TimeSpan>? TimeRemainingChanged;
        public event EventHandler<string>? StatusChanged;

        public async Task<VideoInfo?> LoadVideoInfoAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return null;

                var mediaInfo = await FFProbe.AnalyseAsync(filePath);
                if (mediaInfo?.PrimaryVideoStream == null)
                    return null;

                var videoInfo = new VideoInfo
                {
                    FilePath = filePath,
                    Duration = mediaInfo.Duration,
                    EndTime = mediaInfo.Duration,
                    FileSize = new FileInfo(filePath).Length,
                    Format = Path.GetExtension(filePath).TrimStart('.').ToUpper(),
                    Width = mediaInfo.PrimaryVideoStream.Width,
                    Height = mediaInfo.PrimaryVideoStream.Height,
                    FrameRate = mediaInfo.PrimaryVideoStream.FrameRate,
                    IsLoaded = true
                };

                return videoInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading video info: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> CutVideoAsync(VideoInfo videoInfo, string outputPath, string outputFormat, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!videoInfo.IsLoaded || string.IsNullOrEmpty(videoInfo.FilePath))
                    return false;

                if (videoInfo.StartTime >= videoInfo.EndTime)
                    return false;

                StatusChanged?.Invoke(this, "Initializing...");

                var inputFile = videoInfo.FilePath;
                var duration = videoInfo.CutDuration;
                var startTime = videoInfo.StartTime;

                // Ensure output directory exists
                var outputDir = Path.GetDirectoryName(outputPath);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                StatusChanged?.Invoke(this, "Starting video processing...");

                var conversion = FFMpegArguments
                    .FromFileInput(inputFile, true, options => options
                        .Seek(startTime))
                    .OutputToFile(outputPath, true, options => options
                        .WithDuration(duration)
                        .WithVideoCodec(VideoCodec.LibX264)
                        .WithAudioCodec(AudioCodec.Aac)
                        .WithVariableBitrate(4)
                        .WithVideoFilters(filterOptions => filterOptions
                            .Scale(videoInfo.Width, videoInfo.Height))
                        .WithFastStart());

                var totalFrames = (int)(duration.TotalSeconds * videoInfo.FrameRate);
                var processedFrames = 0;
                var startProcessTime = DateTime.Now;

                await conversion
                    .NotifyOnProgress(progress =>
                    {
                        processedFrames++;
                        var progressPercentage = Math.Min(100.0, (double)processedFrames / totalFrames * 100);
                        ProgressChanged?.Invoke(this, progressPercentage);

                        // Calculate estimated time remaining
                        var elapsed = DateTime.Now - startProcessTime;
                        if (progressPercentage > 0)
                        {
                            var totalEstimated = TimeSpan.FromTicks((long)(elapsed.Ticks / (progressPercentage / 100.0)));
                            var remaining = totalEstimated - elapsed;
                            if (remaining.TotalSeconds > 0)
                            {
                                TimeRemainingChanged?.Invoke(this, remaining);
                            }
                        }

                        StatusChanged?.Invoke(this, $"Processing: {progressPercentage:F1}%");
                    }, TimeSpan.FromMilliseconds(500))
                    .ProcessAsynchronously();

                StatusChanged?.Invoke(this, "Finalizing...");
                ProgressChanged?.Invoke(this, 100.0);
                TimeRemainingChanged?.Invoke(this, TimeSpan.Zero);

                return File.Exists(outputPath);
            }
            catch (OperationCanceledException)
            {
                StatusChanged?.Invoke(this, "Operation cancelled");
                return false;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error cutting video: {ex.Message}");
                return false;
            }
        }

        public static bool IsVideoFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            var videoExtensions = new[]
            {
                ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm",
                ".m4v", ".3gp", ".3g2", ".mts", ".m2ts", ".ts", ".vob",
                ".asf", ".rm", ".rmvb", ".divx", ".xvid", ".f4v"
            };

            return videoExtensions.Contains(extension);
        }

        public static string[] GetSupportedFormats()
        {
            return new[] { "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm" };
        }

        public static string GetOutputFileName(string inputPath, string outputFormat, TimeSpan startTime, TimeSpan endTime)
        {
            var fileName = Path.GetFileNameWithoutExtension(inputPath);
            var startStr = $"{startTime.Hours:D2}h{startTime.Minutes:D2}m{startTime.Seconds:D2}s";
            var endStr = $"{endTime.Hours:D2}h{endTime.Minutes:D2}m{endTime.Seconds:D2}s";
            return $"{fileName}_cut_{startStr}-{endStr}.{outputFormat.ToLowerInvariant()}";
        }
    }
}
