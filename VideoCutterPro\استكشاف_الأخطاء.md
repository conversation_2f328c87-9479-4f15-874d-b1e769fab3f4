# دليل استكشاف الأخطاء | Troubleshooting Guide

## Video Cutter Pro

### 🚨 المشكلة: التطبيق لا يظهر | Application Not Showing

#### الحلول السريعة | Quick Solutions

1. **تحقق من شريط المهام**
   - ابحث عن أيقونة التطبيق في شريط المهام السفلي
   - انقر عليها إذا وجدتها

2. **استخدم Alt+Tab**
   - اضغط Alt+Tab للتنقل بين النوافذ المفتوحة
   - ابحث عن "Video Cutter Pro"

3. **تحقق من النوافذ المخفية**
   - انقر بزر الماوس الأيمن على شريط المهام
   - اختر "إظهار النوافذ جنباً إلى جنب"

4. **إعادة تشغيل التطبيق**
   - أغلق جميع نسخ التطبيق من Task Manager
   - شغل التطبيق مرة أخرى

### 🔧 حلول متقدمة | Advanced Solutions

#### إذا كان التطبيق يبدأ ولكن لا يظهر:

1. **فحص دقة الشاشة**
   ```
   - انقر بزر الماوس الأيمن على سطح المكتب
   - اختر "Display settings"
   - تأكد من أن الدقة مناسبة
   ```

2. **إعادة تعيين موضع النافذة**
   - احذف ملف الإعدادات:
   ```
   %AppData%\VideoCutterPro\settings.json
   ```

3. **تشغيل كمدير**
   - انقر بزر الماوس الأيمن على الملف التنفيذي
   - اختر "Run as administrator"

#### إذا ظهرت رسالة خطأ:

1. **خطأ .NET Runtime**
   ```
   حل: تثبيت .NET 8.0 Runtime
   رابط: https://dotnet.microsoft.com/download/dotnet/8.0
   ```

2. **خطأ Visual C++ Redistributable**
   ```
   حل: تثبيت Visual C++ Redistributable
   رابط: https://aka.ms/vs/17/release/vc_redist.x64.exe
   ```

3. **خطأ في الملفات**
   ```
   حل: إعادة تحميل التطبيق من مصدر موثوق
   ```

### 🖥️ متطلبات النظام | System Requirements Check

#### فحص سريع:
```powershell
# فحص إصدار Windows
winver

# فحص .NET Runtime
dotnet --list-runtimes

# فحص الذاكرة المتاحة
wmic computersystem get TotalPhysicalMemory
```

### 📞 طرق التشغيل البديلة | Alternative Launch Methods

#### الطريقة 1: ملف BAT
```
انقر نقراً مزدوجاً على: تشغيل_التطبيق.bat
```

#### الطريقة 2: Command Prompt
```cmd
cd "C:\path\to\VideoCutterPro"
dotnet run
```

#### الطريقة 3: PowerShell
```powershell
Set-Location "C:\path\to\VideoCutterPro"
.\publish-fixed\VideoCutterPro.exe
```

### 🔍 تشخيص المشاكل | Problem Diagnosis

#### إذا استمرت المشكلة:

1. **فحص Event Viewer**
   ```
   - اضغط Win+R
   - اكتب: eventvwr.msc
   - ابحث عن أخطاء التطبيق
   ```

2. **تشغيل في وضع التشخيص**
   ```cmd
   dotnet run --verbosity detailed
   ```

3. **فحص الملفات المطلوبة**
   ```
   تأكد من وجود:
   - VideoCutterPro.exe
   - جميع ملفات .dll
   - مجلد runtimes (إن وجد)
   ```

### ✅ اختبار سريع | Quick Test

لاختبار أن التطبيق يعمل:

1. افتح Command Prompt
2. انتقل لمجلد التطبيق
3. شغل الأمر:
   ```cmd
   dotnet run --help
   ```

إذا ظهرت رسالة مساعدة، فالتطبيق يعمل بشكل صحيح.

### 📧 طلب المساعدة | Getting Help

إذا لم تحل هذه الخطوات المشكلة:

1. **معلومات مطلوبة للدعم:**
   - إصدار Windows
   - رسالة الخطأ (إن وجدت)
   - خطوات إعادة إنتاج المشكلة

2. **طرق التواصل:**
   - GitHub Issues
   - البريد الإلكتروني: <EMAIL>

---

**ملاحظة:** معظم مشاكل عدم الظهور تحل بإعادة التشغيل أو استخدام Alt+Tab
