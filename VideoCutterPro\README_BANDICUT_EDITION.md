# Video Cutter Pro 🎬 - Bandicut Style Edition

## نظرة عامة | Overview

**Video Cutter Pro - Bandicut Edition** هو تطبيق احترافي لقص الفيديو مصمم بنمط Bandicut الحديث. يوفر واجهة مستخدم متقدمة ومطابقة تماماً لبرنامج Bandicut الأصلي مع دعم كامل للغة العربية والإنجليزية ونظام إدارة وقت متطور.

**Video Cutter Pro - Bandicut Edition** is a professional video cutting application designed with modern Bandicut-style interface. It provides an advanced user interface that perfectly matches the original Bandicut program with full support for Arabic and English languages and sophisticated time management system.

## الميزات الجديدة | New Features

### 🎯 تصميم Bandicut المتقدم | Advanced Bandicut Design
- **واجهة مطابقة لـ Bandicut** - تصميم مطابق تماماً لبرنامج Bandicut الأصلي
- **شريط تحكم أسود احترافي** - أزرار تشغيل وتحكم متقدمة مع تأثيرات بصرية
- **Timeline أخضر متقدم** - شريط زمني بتصميم Bandicut الأصلي مع مؤشر دائري
- **عرض معلومات الفيديو** - إظهار اسم الملف والدقة في شريط العنوان والفيديو
- **أزرار Start خضراء** - تصميم مطابق للأصل مع تأثيرات Hover

### ⏱️ نظام إدارة الوقت المتقدم | Advanced Time Management System
- **تنسيق وقت Bandicut** - H:MM:SS.FF (ساعات:دقائق:ثواني.جزء من الثانية)
- **تحليل وقت ذكي** - دعم صيغ وقت متعددة:
  - `H:MM:SS.FF` - التنسيق الكامل
  - `MM:SS` - التنسيق المختصر
  - `123.45` - الثواني فقط
- **تقريب للإطار** - تقريب تلقائي لأقرب إطار (30 FPS افتراضياً)
- **تحقق متقدم من الوقت** - رسائل خطأ واضحة ومفيدة باللغتين
- **حسابات وقت دقيقة** - نسب مئوية ومدد محسوبة بدقة عالية
- **تحقق بصري** - تغيير لون خلفية مربعات الوقت عند الخطأ

### 🎨 واجهة المستخدم المحسنة | Enhanced User Interface
- **ألوان Bandicut الأصلية**:
  - رمادي فاتح `#F8F8F8` للخلفية
  - أسود `#1A1A1A` لشريط التحكم
  - أخضر `#7CB342` للعناصر التفاعلية
- **تخطيط مطابق للأصل**:
  - مشغل فيديو كبير في المنتصف
  - شريط تحكم أسود في الأسفل
  - منطقة تحرير مع Timeline أخضر
- **تأثيرات بصرية متقدمة**:
  - تأثيرات Hover على الأزرار
  - انتقالات سلسة للألوان
  - مؤشرات بصرية للحالة

### 🔧 ميزات تقنية متقدمة | Advanced Technical Features
- **تزامن Timeline مزدوج** - تحديث متزامن لجميع عناصر الوقت
- **معالجة إدخال ذكية** - تحليل وتصحيح تلقائي لإدخال الوقت
- **تحقق من الصحة في الوقت الفعلي** - فحص فوري للأوقات المدخلة
- **حفظ تلقائي للحالة** - استعادة آخر إعدادات عند إعادة التشغيل

## كيفية الاستخدام | How to Use

### 1. تحميل الفيديو | Loading Video
```
- اسحب وأفلت ملف الفيديو في النافذة الرئيسية
- أو انقر على أيقونة المجلد 📁 لاختيار الملف
- سيظهر اسم الملف في شريط العنوان ومنطقة الفيديو
```

### 2. تحديد نقاط القص | Setting Cut Points
```
- استخدم الشريط الزمني للتنقل في الفيديو
- انقر زر "[" لتعيين نقطة البداية
- انقر زر "]" لتعيين نقطة النهاية
- أو اكتب الأوقات مباشرة في المربعات بصيغة H:MM:SS.FF
```

### 3. التحكم في التشغيل | Playback Control
```
- استخدم أزرار التحكم في الشريط الأسود:
  ⏮ ⏪ ▶ ⏹ ⏸ ⏩ ⏭
- أو انقر على Timeline للانتقال لوقت محدد
- مراقبة الوقت الحالي في العرض المباشر
```

### 4. بدء القص | Start Cutting
```
- تأكد من صحة أوقات البداية والنهاية
- انقر الزر الأخضر "Start"
- راقب تقدم العملية في النافذة المنبثقة
```

## صيغ الوقت المدعومة | Supported Time Formats

### إدخال الوقت | Time Input
- `1:23:45.67` - ساعة:دقيقة:ثانية.جزء من الثانية
- `23:45.67` - دقيقة:ثانية.جزء من الثانية  
- `23:45` - دقيقة:ثانية
- `123.45` - ثواني فقط
- `123` - ثواني صحيحة

### عرض الوقت | Time Display
- **Bandicut Style**: `0:00:26.05`
- **Display Style**: `26:05` (للمدد القصيرة)
- **Filename Style**: `00h26m05s` (لأسماء الملفات)

## التقنيات المستخدمة | Technologies Used

- **Framework**: WPF (.NET 9.0)
- **UI Design**: Custom Bandicut-style XAML
- **Video Processing**: FFMpegCore
- **Time Management**: Advanced TimeSpan handling
- **Architecture**: MVVM Pattern with Services
- **Language**: C# with Arabic/English support

## متطلبات النظام | System Requirements

- **نظام التشغيل**: Windows 10/11 (64-bit)
- **.NET Runtime**: .NET 9.0 Desktop Runtime
- **الذاكرة**: 4 GB RAM (8 GB مستحسن)
- **المساحة**: 500 MB مساحة فارغة
- **المعالج**: Intel/AMD x64 processor

## التشغيل | Running the Application

### طريقة سريعة | Quick Start
```bash
# تشغيل النسخة الاحترافية
dotnet run --project VideoCutterPro/VideoCutterPro.csproj -- professional

# أو استخدام ملف BAT
تشغيل_احترافي.bat
```

### البناء من المصدر | Build from Source
```bash
# استنساخ المشروع
git clone [repository-url]
cd VideoCutterPro

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run -- professional
```

## الإصدارات | Versions

### الإصدار الحالي: v2.0.0 - Bandicut Edition
- **تاريخ الإصدار**: 2025-06-19
- **الميزات الجديدة**: 
  - تصميم Bandicut المطابق للأصل
  - نظام إدارة وقت متقدم
  - واجهة محسنة بالكامل
  - دعم تنسيقات وقت متعددة

### الإصدارات السابقة
- **v1.0.0**: الإصدار الأساسي
- **v1.1.0**: تحسينات الأداء
- **v1.5.0**: دعم اللغة العربية المحسن

## المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم | Support

- **GitHub Issues**: [رابط المشاكل]
- **Email**: <EMAIL>
- **Documentation**: [رابط التوثيق]

---

**ملاحظة**: هذا الإصدار مصمم خصيصاً لمحاكاة تجربة Bandicut الأصلية مع تحسينات إضافية.

**Note**: This version is specifically designed to replicate the original Bandicut experience with additional enhancements.
