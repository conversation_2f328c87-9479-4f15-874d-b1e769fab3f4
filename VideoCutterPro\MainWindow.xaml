﻿<Window x:Class="VideoCutterPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VideoCutterPro"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Video Cutter Pro"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        Topmost="True"
        AllowDrop="True"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <!-- Converters -->
        <local:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter"/>
        <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:FlowDirectionConverter x:Key="FlowDirectionConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <!-- Title Bar -->
            <RowDefinition Height="40"/>
            <!-- Main Content -->
            <RowDefinition Height="*"/>
            <!-- Status Bar -->
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- Custom Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                    <materialDesign:PackIcon Kind="VideoVintage" Foreground="White" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding LocalizationService.AppTitle}"
                               Foreground="White"
                               FontSize="14"
                               FontWeight="Medium"
                               VerticalAlignment="Center"
                               Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Window Controls -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="SettingsButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            ToolTip="{Binding LocalizationService.Settings}"
                            Click="SettingsButton_Click">
                        <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"/>
                    </Button>
                    <Button Name="MinimizeButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            Click="MinimizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                    </Button>
                    <Button Name="MaximizeButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            Click="MaximizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16"/>
                    </Button>
                    <Button Name="CloseButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="WindowClose" Width="16" Height="16"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20" FlowDirection="{Binding LocalizationService.IsRightToLeft, Converter={StaticResource FlowDirectionConverter}}">
            <Grid.RowDefinitions>
                <!-- Video Preview Area -->
                <RowDefinition Height="2*"/>
                <!-- Controls Area -->
                <RowDefinition Height="Auto"/>
                <!-- Timeline Area -->
                <RowDefinition Height="Auto"/>
                <!-- Info Area -->
                <RowDefinition Height="Auto"/>
                <!-- Action Buttons -->
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Video Preview Area -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,20">
                <Grid>
                    <!-- Video Player -->
                    <Border Background="Black">
                        <MediaElement Name="VideoPlayer"
                                      LoadedBehavior="Manual"
                                      UnloadedBehavior="Manual"
                                      Stretch="Uniform"
                                      MediaOpened="VideoPlayer_MediaOpened"
                                      MediaEnded="VideoPlayer_MediaEnded"
                                      MediaFailed="VideoPlayer_MediaFailed"/>
                    </Border>

                    <!-- Drag & Drop Overlay -->
                    <Border Name="DragDropOverlay"
                            Background="{StaticResource SurfaceBrush}"
                            Visibility="{Binding VideoInfo.IsLoaded, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Invert}">
                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="VideoPlus" Width="80" Height="80" Foreground="{StaticResource TextSecondaryBrush}"/>
                            <TextBlock Text="{Binding LocalizationService.DragDropHint}"
                                       FontSize="16"
                                       Foreground="{StaticResource TextSecondaryBrush}"
                                       TextAlignment="Center"
                                       Margin="20"
                                       TextWrapping="Wrap"/>
                            <Button Content="{Binding LocalizationService.SelectVideo}"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="SelectVideoButton_Click"
                                    Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Video Controls Overlay -->
                    <Grid Name="VideoControlsOverlay"
                          VerticalAlignment="Bottom"
                          Background="#80000000"
                          Visibility="{Binding VideoInfo.IsLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                            <Button Name="PlayButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Foreground="White"
                                    Click="PlayButton_Click"
                                    ToolTip="{Binding LocalizationService.Play}">
                                <materialDesign:PackIcon Kind="Play" Width="30" Height="30"/>
                            </Button>
                            <Button Name="PauseButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Foreground="White"
                                    Click="PauseButton_Click"
                                    ToolTip="{Binding LocalizationService.Pause}">
                                <materialDesign:PackIcon Kind="Pause" Width="30" Height="30"/>
                            </Button>
                            <Button Name="StopButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Foreground="White"
                                    Click="StopButton_Click"
                                    ToolTip="{Binding LocalizationService.Stop}">
                                <materialDesign:PackIcon Kind="Stop" Width="30" Height="30"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Timeline Controls -->
            <materialDesign:Card Grid.Row="1" Margin="0,0,0,20">
                <StackPanel Margin="20">
                    <!-- Timeline Slider -->
                    <Grid Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Time Labels -->
                        <Grid Grid.Row="0" Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                       Text="{Binding VideoInfo.StartTime, Converter={StaticResource TimeSpanToStringConverter}}"
                                       HorizontalAlignment="Left"
                                       FontSize="12"
                                       Foreground="{StaticResource AccentBrush}"/>

                            <TextBlock Grid.Column="1"
                                       Text="{Binding VideoInfo.CurrentPosition, Converter={StaticResource TimeSpanToStringConverter}}"
                                       HorizontalAlignment="Center"
                                       FontSize="14"
                                       FontWeight="Bold"/>

                            <TextBlock Grid.Column="2"
                                       Text="{Binding VideoInfo.EndTime, Converter={StaticResource TimeSpanToStringConverter}}"
                                       HorizontalAlignment="Right"
                                       FontSize="12"
                                       Foreground="{StaticResource AccentBrush}"/>
                        </Grid>

                        <!-- Timeline Slider -->
                        <Slider Grid.Row="1"
                                Name="TimelineSlider"
                                Style="{StaticResource TimelineSliderStyle}"
                                Minimum="0"
                                Maximum="{Binding VideoInfo.Duration.TotalSeconds}"
                                Value="{Binding VideoInfo.CurrentPosition.TotalSeconds}"
                                IsEnabled="{Binding VideoInfo.IsLoaded}"
                                ValueChanged="TimelineSlider_ValueChanged"
                                Thumb.DragStarted="TimelineSlider_DragStarted"
                                Thumb.DragCompleted="TimelineSlider_DragCompleted"/>
                    </Grid>

                    <!-- Cut Controls -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0"
                                Name="SetStartTimeButton"
                                Content="{Binding LocalizationService.SetStartTime}"
                                Style="{StaticResource ModernButtonStyle}"
                                Click="SetStartTimeButton_Click"
                                IsEnabled="{Binding VideoInfo.IsLoaded}"
                                Margin="0,0,10,0">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="SkipPrevious" Width="16" Height="16" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Grid.Column="1"
                                Name="SetEndTimeButton"
                                Content="{Binding LocalizationService.SetEndTime}"
                                Style="{StaticResource ModernButtonStyle}"
                                Click="SetEndTimeButton_Click"
                                IsEnabled="{Binding VideoInfo.IsLoaded}"
                                Margin="10,0,0,0">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="SkipNext" Width="16" Height="16" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding}" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Video Information -->
            <materialDesign:Card Grid.Row="2" Margin="0,0,0,20"
                                 Visibility="{Binding VideoInfo.IsLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid Margin="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Duration Info -->
                    <StackPanel Grid.Column="0" Grid.Row="0">
                        <TextBlock Text="{Binding LocalizationService.Duration}"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding VideoInfo.Duration, Converter={StaticResource TimeSpanToStringConverter}}"
                                   FontSize="14"
                                   FontWeight="Bold"/>
                    </StackPanel>

                    <!-- Cut Duration Info -->
                    <StackPanel Grid.Column="1" Grid.Row="0">
                        <TextBlock Text="{Binding LocalizationService.CutDuration}"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding VideoInfo.CutDuration, Converter={StaticResource TimeSpanToStringConverter}}"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Foreground="{StaticResource AccentBrush}"/>
                    </StackPanel>

                    <!-- File Size Info -->
                    <StackPanel Grid.Column="2" Grid.Row="0">
                        <TextBlock Text="{Binding LocalizationService.FileSize}"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding VideoInfo.FileSizeFormatted}"
                                   FontSize="14"
                                   FontWeight="Bold"/>
                    </StackPanel>

                    <!-- Resolution Info -->
                    <StackPanel Grid.Column="3" Grid.Row="0">
                        <TextBlock Text="{Binding LocalizationService.Resolution}"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding VideoInfo.Resolution}"
                                   FontSize="14"
                                   FontWeight="Bold"/>
                    </StackPanel>

                    <!-- File Name (spans all columns) -->
                    <StackPanel Grid.Column="0" Grid.ColumnSpan="4" Grid.Row="1" Margin="0,15,0,0">
                        <TextBlock Text="File Name"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                        <TextBlock Text="{Binding VideoInfo.FileName}"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   TextTrimming="CharacterEllipsis"
                                   ToolTip="{Binding VideoInfo.FilePath}"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- External Controls -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                    <Button Name="ExternalPlayButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Click="PlayButton_Click"
                            IsEnabled="{Binding VideoInfo.IsLoaded}"
                            ToolTip="{Binding LocalizationService.Play}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Play" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding LocalizationService.Play}" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Name="ExternalStopButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Click="StopButton_Click"
                            IsEnabled="{Binding VideoInfo.IsLoaded}"
                            ToolTip="{Binding LocalizationService.Stop}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Stop" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding LocalizationService.Stop}" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- Start Cutting Button -->
                <Button Grid.Column="1"
                        Name="StartCuttingButton"
                        Content="{Binding LocalizationService.StartCutting}"
                        Style="{StaticResource StartButtonStyle}"
                        Click="StartCuttingButton_Click"
                        IsEnabled="{Binding CanStartCutting}"
                        Width="200">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentCut" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding}" Margin="10,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" BorderBrush="{StaticResource TextSecondaryBrush}" BorderThickness="0,1,0,0">
            <Grid Margin="10,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Name="StatusTextBlock"
                           Text="Ready"
                           VerticalAlignment="Center"
                           FontSize="12"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding LocalizationService.CurrentLanguage}"
                               VerticalAlignment="Center"
                               FontSize="12"
                               Margin="0,0,10,0"/>
                    <Rectangle Width="1" Fill="{StaticResource TextSecondaryBrush}" Margin="0,2"/>
                    <TextBlock Text="v1.0.0"
                               VerticalAlignment="Center"
                               FontSize="12"
                               Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
