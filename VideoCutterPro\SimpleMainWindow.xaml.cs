using System.Windows;
using Microsoft.Win32;

namespace VideoCutterPro;

public partial class SimpleMainWindow : Window
{
    public SimpleMainWindow()
    {
        InitializeComponent();
        
        // Ensure window is visible
        WindowStartupLocation = WindowStartupLocation.CenterScreen;
        WindowState = WindowState.Normal;
        Topmost = true;
        
        // Show and activate
        Show();
        Activate();
        Focus();
        
        // Remove topmost after showing
        Dispatcher.BeginInvoke(new Action(() => Topmost = false), System.Windows.Threading.DispatcherPriority.ApplicationIdle);
    }

    private void SelectVideoButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف فيديو | Select Video File",
                Filter = "Video Files|*.mp4;*.avi;*.mkv;*.mov;*.wmv;*.flv;*.webm|All Files|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                MessageBox.Show($"تم اختيار الملف:\n{openFileDialog.FileName}\n\nFile selected:\n{openFileDialog.FileName}", 
                               "نجح | Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}\nError: {ex.Message}", 
                           "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void StartCuttingButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("ميزة القص ستكون متاحة قريباً!\nCutting feature coming soon!", 
                       "قريباً | Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        var settingsMessage = @"الإعدادات المتاحة:
• اللغة: العربية/الإنجليزية
• المظهر: فاتح/داكن
• مجلد الحفظ
• صيغة الإخراج

Available Settings:
• Language: Arabic/English  
• Theme: Light/Dark
• Output Directory
• Output Format";

        MessageBox.Show(settingsMessage, "الإعدادات | Settings", MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
