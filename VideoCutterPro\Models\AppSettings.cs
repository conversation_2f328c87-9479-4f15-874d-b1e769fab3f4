using Newtonsoft.Json;

namespace VideoCutterPro.Models
{
    public class AppSettings
    {
        [JsonProperty("language")]
        public string Language { get; set; } = "ar"; // Default to Arabic

        [JsonProperty("theme")]
        public string Theme { get; set; } = "Light"; // Light or Dark

        [JsonProperty("outputDirectory")]
        public string OutputDirectory { get; set; } = Environment.GetFolderPath(Environment.SpecialFolder.Videos);

        [JsonProperty("defaultOutputFormat")]
        public string DefaultOutputFormat { get; set; } = "mp4";

        [JsonProperty("showCountdown")]
        public bool ShowCountdown { get; set; } = true;

        [JsonProperty("autoCloseAfterCutting")]
        public bool AutoCloseAfterCutting { get; set; } = false;

        [JsonProperty("playNotificationSound")]
        public bool PlayNotificationSound { get; set; } = true;

        [JsonProperty("windowWidth")]
        public double WindowWidth { get; set; } = 1200;

        [JsonProperty("windowHeight")]
        public double WindowHeight { get; set; } = 800;

        [JsonProperty("windowLeft")]
        public double WindowLeft { get; set; } = 100;

        [JsonProperty("windowTop")]
        public double WindowTop { get; set; } = 100;

        [JsonProperty("isMaximized")]
        public bool IsMaximized { get; set; } = false;

        public static AppSettings LoadSettings()
        {
            try
            {
                string settingsPath = GetSettingsPath();
                if (File.Exists(settingsPath))
                {
                    string json = File.ReadAllText(settingsPath);
                    return JsonConvert.DeserializeObject<AppSettings>(json) ?? new AppSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }
            return new AppSettings();
        }

        public void SaveSettings()
        {
            try
            {
                string settingsPath = GetSettingsPath();
                string directory = Path.GetDirectoryName(settingsPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(settingsPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        private static string GetSettingsPath()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "VideoCutterPro", "settings.json");
        }
    }
}
