# دليل البناء والتجميع | Build and Deployment Guide

## Video Cutter Pro

### بناء المشروع للتطوير | Development Build

#### 1. بناء Debug
```bash
dotnet build --configuration Debug
```

#### 2. تشغيل في وضع التطوير
```bash
dotnet run --configuration Debug
```

### بناء المشروع للإنتاج | Production Build

#### 1. بناء Release
```bash
dotnet build --configuration Release
```

#### 2. إنشاء حزمة النشر | Create Deployment Package

##### للنظام الحالي | For Current System
```bash
dotnet publish -c Release -o ./publish
```

##### لنظام Windows x64 | For Windows x64
```bash
dotnet publish -c Release -r win-x64 --self-contained true -o ./publish/win-x64
```

##### لنظام Windows x86 | For Windows x86
```bash
dotnet publish -c Release -r win-x86 --self-contained true -o ./publish/win-x86
```

##### ملف تنفيذي واحد | Single File Executable
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o ./publish/single-file
```

### تحسين الحجم | Size Optimization

#### إزالة الملفات غير المستخدمة | Remove Unused Files
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishTrimmed=true -o ./publish/trimmed
```

#### ضغط إضافي | Additional Compression
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -p:EnableCompressionInSingleFile=true -o ./publish/optimized
```

### إنشاء مثبت | Create Installer

#### باستخدام Inno Setup (Windows)

1. **تثبيت Inno Setup**
   - تحميل من: https://jrsoftware.org/isinfo.php

2. **إنشاء ملف الإعداد** (`installer.iss`):
```inno
[Setup]
AppName=Video Cutter Pro
AppVersion=1.0.0
AppPublisher=Video Cutter Pro Team
AppPublisherURL=https://github.com/videocutterpro
DefaultDirName={autopf}\Video Cutter Pro
DefaultGroupName=Video Cutter Pro
OutputDir=installer
OutputBaseFilename=VideoCutterPro-Setup-v1.0.0
Compression=lzma
SolidCompression=yes
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "publish\win-x64\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\Video Cutter Pro"; Filename: "{app}\VideoCutterPro.exe"
Name: "{autodesktop}\Video Cutter Pro"; Filename: "{app}\VideoCutterPro.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\VideoCutterPro.exe"; Description: "{cm:LaunchProgram,Video Cutter Pro}"; Flags: nowait postinstall skipifsilent
```

3. **تجميع المثبت**
```bash
iscc installer.iss
```

### اختبار البناء | Testing Build

#### 1. اختبار الوظائف الأساسية
- [ ] تشغيل التطبيق
- [ ] تحميل ملف فيديو
- [ ] تحديد نقاط القص
- [ ] قص الفيديو
- [ ] حفظ الملف

#### 2. اختبار الواجهة
- [ ] تبديل اللغة
- [ ] تبديل المظهر
- [ ] فتح نافذة الإعدادات
- [ ] السحب والإفلات

#### 3. اختبار الأداء
- [ ] ملفات فيديو كبيرة (>1GB)
- [ ] ملفات متعددة الصيغ
- [ ] قص مقاطع طويلة (>30 دقيقة)

### نشر التطبيق | Application Deployment

#### 1. GitHub Releases
```bash
# إنشاء tag للإصدار
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# رفع ملفات البناء إلى GitHub Releases
```

#### 2. Microsoft Store (اختياري)
- تحويل إلى MSIX package
- رفع إلى Partner Center

#### 3. Chocolatey (اختياري)
- إنشاء Chocolatey package
- نشر في Chocolatey Gallery

### متطلبات النشر | Deployment Requirements

#### الملفات المطلوبة | Required Files
```
VideoCutterPro/
├── VideoCutterPro.exe
├── VideoCutterPro.dll
├── VideoCutterPro.deps.json
├── VideoCutterPro.runtimeconfig.json
├── FFMpegCore.dll
├── MaterialDesignThemes.Wpf.dll
├── MaterialDesignColors.dll
├── Newtonsoft.Json.dll
└── runtimes/
    └── win-x64/
        └── native/
            └── (FFmpeg binaries)
```

#### متغيرات البيئة | Environment Variables
```bash
# اختياري: مسار FFmpeg مخصص
FFMPEG_PATH=C:\path\to\ffmpeg

# اختياري: مجلد الإعدادات
VIDEOCUTTERPRO_SETTINGS_PATH=C:\path\to\settings
```

### استكشاف أخطاء البناء | Build Troubleshooting

#### مشكلة: فشل في البناء
```bash
# تنظيف المشروع
dotnet clean

# استعادة الحزم
dotnet restore

# إعادة البناء
dotnet build
```

#### مشكلة: ملفات مفقودة في النشر
```bash
# التأكد من تضمين جميع الملفات
dotnet publish --verbosity detailed
```

#### مشكلة: حجم كبير للملف التنفيذي
```bash
# استخدام التحسين
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishTrimmed=true -p:PublishSingleFile=true
```

### قائمة مراجعة النشر | Deployment Checklist

- [ ] اختبار على أنظمة Windows مختلفة
- [ ] التأكد من عمل جميع الميزات
- [ ] فحص الأمان (Windows Defender, VirusTotal)
- [ ] تحديث ملفات التوثيق
- [ ] إنشاء Release Notes
- [ ] رفع الملفات إلى GitHub
- [ ] إشعار المستخدمين بالتحديث

### معلومات الإصدار | Version Information

#### الإصدار الحالي: v1.0.0
- تاريخ الإصدار: 2025-06-19
- الميزات الجديدة: جميع الميزات الأساسية
- إصلاحات الأخطاء: N/A (الإصدار الأول)

#### الإصدارات القادمة
- v1.1.0: دعم Batch Processing
- v1.2.0: تأثيرات الفيديو الأساسية
- v2.0.0: محرر فيديو متقدم

---

**ملاحظة:** تأكد من اختبار جميع البناءات قبل النشر النهائي
