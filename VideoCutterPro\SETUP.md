# دليل التثبيت والتشغيل | Setup and Installation Guide

## Video Cutter Pro

### المتطلبات الأساسية | Prerequisites

#### 1. تثبيت .NET 8.0 Runtime
قم بتحميل وتثبيت .NET 8.0 Runtime من الموقع الرسمي:
- [تحميل .NET 8.0](https://dotnet.microsoft.com/download/dotnet/8.0)
- اختر "Download .NET 8.0 Runtime" للنظام الخاص بك

#### 2. تثبيت FFmpeg (اختياري)
للحصول على أفضل أداء، يُنصح بتثبيت FFmpeg:
- [تحميل FFmpeg](https://ffmpeg.org/download.html)
- أضف مجلد FFmpeg إلى متغير البيئة PATH

### طرق التثبيت | Installation Methods

#### الطريقة الأولى: تشغيل من الكود المصدري | Method 1: Run from Source

1. **تحميل المشروع**
```bash
git clone [repository-url]
cd VideoCutterPro
```

2. **استعادة الحزم**
```bash
dotnet restore
```

3. **بناء المشروع**
```bash
dotnet build --configuration Release
```

4. **تشغيل التطبيق**
```bash
dotnet run
```

#### الطريقة الثانية: إنشاء ملف تنفيذي | Method 2: Create Executable

1. **إنشاء ملف تنفيذي مستقل**
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

2. **العثور على الملف التنفيذي**
الملف سيكون في: `bin\Release\net8.0-windows\win-x64\publish\VideoCutterPro.exe`

3. **تشغيل التطبيق**
انقر نقراً مزدوجاً على الملف التنفيذي

### التحقق من التثبيت | Verify Installation

#### 1. فحص .NET Runtime
```bash
dotnet --version
```
يجب أن يظهر الإصدار 8.0 أو أحدث

#### 2. فحص FFmpeg (اختياري)
```bash
ffmpeg -version
```

### إعداد التطبيق لأول مرة | First-Time Setup

#### 1. تشغيل التطبيق
- شغل التطبيق من خلال الملف التنفيذي أو `dotnet run`

#### 2. إعداد اللغة
- انقر على زر "الإعدادات" في الشريط العلوي
- اختر اللغة المفضلة (العربية أو الإنجليزية)
- انقر "حفظ"

#### 3. إعداد مجلد الحفظ
- في نافذة الإعدادات، انقر "تصفح" بجانب "مجلد الحفظ"
- اختر المجلد المطلوب لحفظ الفيديوهات المقصوصة
- انقر "حفظ"

#### 4. اختيار المظهر
- اختر بين الوضع الفاتح والداكن
- سيتم تطبيق التغيير فوراً

### استكشاف الأخطاء | Troubleshooting

#### مشكلة: التطبيق لا يبدأ
**الحل:**
1. تأكد من تثبيت .NET 8.0 Runtime
2. تشغيل Command Prompt كمدير وتنفيذ:
```bash
dotnet --list-runtimes
```

#### مشكلة: خطأ في قص الفيديو
**الحل:**
1. تأكد من أن ملف الفيديو غير تالف
2. تأكد من وجود مساحة كافية في القرص الصلب
3. جرب صيغة إخراج مختلفة

#### مشكلة: الواجهة تظهر باللغة الإنجليزية
**الحل:**
1. افتح الإعدادات
2. غير اللغة إلى العربية
3. أعد تشغيل التطبيق

#### مشكلة: بطء في معالجة الفيديو
**الحل:**
1. تأكد من تثبيت FFmpeg
2. أغلق البرامج الأخرى التي تستهلك الذاكرة
3. استخدم ملفات فيديو بحجم أصغر للاختبار

### نصائح للاستخدام الأمثل | Optimization Tips

#### 1. أداء أفضل
- استخدم SSD بدلاً من HDD للحفظ
- تأكد من وجود ذاكرة RAM كافية (8GB أو أكثر)
- أغلق البرامج غير الضرورية أثناء المعالجة

#### 2. جودة أفضل
- استخدم ملفات الفيديو الأصلية بدون ضغط
- اختر صيغة MP4 للحصول على أفضل توافق
- تجنب قص مقاطع قصيرة جداً (أقل من ثانية واحدة)

#### 3. سير عمل فعال
- نظم ملفاتك في مجلدات منفصلة
- استخدم أسماء وصفية للملفات
- احتفظ بنسخة احتياطية من الملفات الأصلية

### الدعم الفني | Technical Support

#### معلومات النظام المطلوبة عند طلب الدعم:
1. إصدار Windows
2. إصدار .NET Runtime
3. رسالة الخطأ الكاملة (إن وجدت)
4. خطوات إعادة إنتاج المشكلة

#### طرق التواصل:
- GitHub Issues: [رابط المستودع]
- البريد الإلكتروني: <EMAIL>

### تحديثات التطبيق | Application Updates

#### التحقق من التحديثات:
1. زيارة صفحة المشروع على GitHub
2. مراجعة قسم "Releases"
3. تحميل الإصدار الأحدث

#### تطبيق التحديثات:
1. إغلاق التطبيق الحالي
2. استبدال الملفات القديمة بالجديدة
3. تشغيل التطبيق الجديد

---

**ملاحظة:** هذا الدليل يغطي الإعداد الأساسي. للاستخدام المتقدم، راجع ملف README.md
