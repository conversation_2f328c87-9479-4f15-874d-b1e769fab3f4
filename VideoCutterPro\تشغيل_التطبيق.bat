@echo off
echo ========================================
echo       Video Cutter Pro
echo       تطبيق قص الفيديوهات الاحترافي
echo ========================================
echo.

echo جاري تشغيل التطبيق...
echo Starting application...
echo.

cd /d "%~dp0"

REM Try to run the published version first
if exist "publish-fixed\VideoCutterPro.exe" (
    echo تشغيل النسخة المحدثة...
    echo Running updated version...
    start "" "publish-fixed\VideoCutterPro.exe"
    goto :success
)

REM Try the original published version
if exist "publish\VideoCutterPro.exe" (
    echo تشغيل النسخة الأصلية...
    echo Running original version...
    start "" "publish\VideoCutterPro.exe"
    goto :success
)

REM Try to run from source
echo تشغيل من الكود المصدري...
echo Running from source code...
dotnet run
goto :success

:success
echo.
echo تم تشغيل التطبيق بنجاح!
echo Application started successfully!
echo.
echo إذا لم تظهر النافذة، تحقق من:
echo If the window doesn't appear, check:
echo - شريط المهام (Taskbar)
echo - النوافذ المخفية (Hidden windows)
echo - اضغط Alt+Tab للتنقل بين النوافذ
echo.
pause
