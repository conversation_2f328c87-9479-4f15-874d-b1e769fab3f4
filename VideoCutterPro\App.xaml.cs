﻿using System.Configuration;
using System.Data;
using System.Windows;

namespace VideoCutterPro;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // Handle unhandled exceptions
        DispatcherUnhandledException += App_DispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

        base.OnStartup(e);

        // Show main window explicitly
        var mainWindow = new MainWindow();
        mainWindow.Show();
        mainWindow.Activate();
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"خطأ في التطبيق: {e.Exception.Message}\n\nApplication Error: {e.Exception.Message}",
                       "خطأ | Error", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true;
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        MessageBox.Show($"خطأ غير متوقع: {exception?.Message}\n\nUnexpected Error: {exception?.Message}",
                       "خطأ حرج | Critical Error", MessageBoxButton.OK, MessageBoxImage.Error);
    }
}

