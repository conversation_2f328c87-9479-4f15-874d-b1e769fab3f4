@echo off
title Video Cutter Pro - Professional Interface

echo.
echo ==========================================
echo    Video Cutter Pro
echo    الواجهة الاحترافية - Professional Interface
echo ==========================================
echo.
echo جاري تشغيل الواجهة الاحترافية...
echo Starting professional interface...
echo.

cd /d "%~dp0"

REM Try the professional version first
if exist "publish-professional\VideoCutterPro.exe" (
    echo تشغيل الواجهة الاحترافية...
    echo Running professional interface...
    start "" "publish-professional\VideoCutterPro.exe"
    echo.
    echo تم تشغيل التطبيق!
    echo Application started!
    echo.
    echo الواجهة الجديدة تشبه برامج التحرير الاحترافية
    echo The new interface resembles professional editing software
    echo.
    echo ابحث عن النافذة في شريط المهام
    echo Look for the window in the taskbar
    echo.
    timeout /t 5 >nul
    exit
)

echo تشغيل من الكود المصدري...
echo Running from source...
dotnet run

echo.
echo انتهى التشغيل
echo Execution completed
pause
