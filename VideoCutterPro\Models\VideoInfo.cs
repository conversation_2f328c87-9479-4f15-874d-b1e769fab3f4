using System.ComponentModel;
using System.IO;

namespace VideoCutterPro.Models
{
    public class VideoInfo : INotifyPropertyChanged
    {
        private string _filePath = string.Empty;
        private TimeSpan _duration = TimeSpan.Zero;
        private TimeSpan _startTime = TimeSpan.Zero;
        private TimeSpan _endTime = TimeSpan.Zero;
        private TimeSpan _currentPosition = TimeSpan.Zero;
        private bool _isLoaded = false;
        private string _fileName = string.Empty;
        private long _fileSize = 0;
        private string _format = string.Empty;
        private int _width = 0;
        private int _height = 0;
        private double _frameRate = 0;

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                OnPropertyChanged();
                FileName = Path.GetFileName(value);
            }
        }

        public string FileName
        {
            get => _fileName;
            set
            {
                _fileName = value;
                OnPropertyChanged();
            }
        }

        public TimeSpan Duration
        {
            get => _duration;
            set
            {
                _duration = value;
                OnPropertyChanged();
                // Update end time to duration if not set
                if (EndTime == TimeSpan.Zero)
                {
                    EndTime = value;
                }
            }
        }

        public TimeSpan StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CutDuration));
            }
        }

        public TimeSpan EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CutDuration));
            }
        }

        public TimeSpan CurrentPosition
        {
            get => _currentPosition;
            set
            {
                _currentPosition = value;
                OnPropertyChanged();
            }
        }

        public TimeSpan CutDuration => EndTime - StartTime;

        public bool IsLoaded
        {
            get => _isLoaded;
            set
            {
                _isLoaded = value;
                OnPropertyChanged();
            }
        }

        public long FileSize
        {
            get => _fileSize;
            set
            {
                _fileSize = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FileSizeFormatted));
            }
        }

        public string FileSizeFormatted
        {
            get
            {
                if (FileSize == 0) return "0 B";
                
                string[] sizes = { "B", "KB", "MB", "GB", "TB" };
                double len = FileSize;
                int order = 0;
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                return $"{len:0.##} {sizes[order]}";
            }
        }

        public string Format
        {
            get => _format;
            set
            {
                _format = value;
                OnPropertyChanged();
            }
        }

        public int Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(Resolution));
            }
        }

        public int Height
        {
            get => _height;
            set
            {
                _height = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(Resolution));
            }
        }

        public string Resolution => Width > 0 && Height > 0 ? $"{Width}x{Height}" : "Unknown";

        public double FrameRate
        {
            get => _frameRate;
            set
            {
                _frameRate = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Reset()
        {
            FilePath = string.Empty;
            Duration = TimeSpan.Zero;
            StartTime = TimeSpan.Zero;
            EndTime = TimeSpan.Zero;
            CurrentPosition = TimeSpan.Zero;
            IsLoaded = false;
            FileSize = 0;
            Format = string.Empty;
            Width = 0;
            Height = 0;
            FrameRate = 0;
        }
    }
}
