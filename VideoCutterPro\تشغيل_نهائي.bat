@echo off
title Video Cutter Pro - Complete Version

echo.
echo ==========================================
echo    Video Cutter Pro
echo    النسخة النهائية الكاملة - Complete Version
echo ==========================================
echo.
echo جاري تشغيل النسخة النهائية...
echo Starting complete version...
echo.

cd /d "%~dp0"

REM Try the complete version first
if exist "publish-complete\VideoCutterPro.exe" (
    echo تشغيل النسخة النهائية الكاملة...
    echo Running complete final version...
    start "" "publish-complete\VideoCutterPro.exe"
    echo.
    echo تم تشغيل التطبيق بنجاح!
    echo Application started successfully!
    echo.
    echo الان ستجد جميع الازرار:
    echo Now you will find all buttons:
    echo - زر Start اخضر كبير في الاسفل
    echo - Large green Start button at bottom
    echo - زر اختيار الفيديو (مجلد) في الاسفل
    echo - Video selection button (folder) at bottom
    echo - جميع ازرار التحكم واضحة
    echo - All control buttons are clear
    echo - امكانية تغيير حجم النافذة
    echo - Window resizing capability
    echo - واجهة مطابقة للصورة المطلوبة
    echo - Interface matching the requested image
    echo.
    echo ابحث عن النافذة في شريط المهام
    echo Look for the window in the taskbar
    echo.
    timeout /t 5 >nul
    exit
)

echo تشغيل من الكود المصدري...
echo Running from source...
dotnet run

echo.
echo انتهى التشغيل
echo Execution completed
pause
