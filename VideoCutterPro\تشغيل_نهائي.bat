@echo off
title Video Cutter Pro - PERFECT SIZE VERSION

echo.
echo ==========================================
echo    Video Cutter Pro
echo    النسخة بالحجم المثالي - PERFECT SIZE VERSION
echo ==========================================
echo.
echo جاري تشغيل النسخة بالحجم المثالي...
echo Starting perfect size version...
echo.

cd /d "%~dp0"

echo تشغيل من الكود المصدري...
echo Running from source...
dotnet run

echo.
echo تم تشغيل التطبيق بنجاح!
echo Application started successfully!
echo.
echo الان ستجد النسخة بالحجم المثالي:
echo Now you will find the perfect size version:
echo.
echo ✅ حجم نافذة مثالي عند الفتح (625x908)
echo ✅ Perfect window size on startup (625x908)
echo ✅ شريط عنوان احترافي مخصص
echo ✅ Custom professional title bar
echo ✅ زر Start اخضر واضح (140x55) - مرئي بالكامل
echo ✅ Clear green Start button (140x55) - FULLY VISIBLE
echo ✅ زر المجلد واضح (75x55) - مرئي بالكامل
echo ✅ Clear folder button (75x55) - FULLY VISIBLE
echo ✅ جميع الازرار واضحة ومرئية بالكامل
echo ✅ All buttons clear and FULLY VISIBLE
echo ✅ منطقة تحكم مناسبة (220 بكسل)
echo ✅ Appropriate control area (220 pixels)
echo ✅ امكانية تكبير/تصغير من جميع الجهات
echo ✅ Full resize capability from ALL SIDES
echo ✅ يمين، يسار، اعلى، اسفل - كل الاتجاهات
echo ✅ Right, left, top, bottom - ALL DIRECTIONS
echo ✅ مؤشر الماوس يتغير عند الحواف للتحكم بالحجم
echo ✅ Mouse cursor changes at edges for resizing
echo ✅ واجهة احترافية مطابقة للصورة المطلوبة
echo ✅ Professional interface matching requested image
echo ✅ حجم مناسب وقابل للتحكم
echo ✅ Appropriate size and fully controllable
echo.
echo ابحث عن النافذة في شريط المهام
echo Look for the window in the taskbar
echo.
echo انتهى التشغيل
echo Execution completed
pause
