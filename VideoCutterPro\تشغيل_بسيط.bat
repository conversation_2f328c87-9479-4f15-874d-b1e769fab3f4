@echo off
title Video Cutter Pro - تطبيق قص الفيديوهات

echo.
echo ==========================================
echo    Video Cutter Pro
echo    تطبيق قص الفيديوهات الاحترافي
echo ==========================================
echo.
echo جاري تشغيل التطبيق...
echo Starting application...
echo.

cd /d "%~dp0"

REM Try the simple version first
if exist "publish-simple\VideoCutterPro.exe" (
    echo تشغيل النسخة المبسطة...
    echo Running simple version...
    start "" "publish-simple\VideoCutterPro.exe"
    echo.
    echo تم تشغيل التطبيق!
    echo Application started!
    echo.
    echo ابحث عن النافذة في شريط المهام
    echo Look for the window in the taskbar
    echo.
    timeout /t 3 >nul
    exit
)

echo تشغيل من الكود المصدري...
echo Running from source...
dotnet run

echo.
echo انتهى التشغيل
echo Execution completed
pause
